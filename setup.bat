@echo off
title ASCAES - First Time Setup
color 0A

echo.
echo ========================================
echo   ASCAES - First Time Setup
echo ========================================
echo.
echo This will install all dependencies and test the system.
echo.

REM Check prerequisites
echo [1/4] Checking prerequisites...

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found  
    echo Please install Python from https://python.org/
    pause
    exit /b 1
)

echo Prerequisites OK
echo.

REM Install dependencies
echo [2/4] Installing dependencies...
echo This may take a few minutes...

call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install root dependencies
    pause
    exit /b 1
)

cd frontend-react
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

cd backend-python
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERROR: Failed to install Python dependencies
    pause
    exit /b 1
)
cd ..

echo Dependencies installed successfully!
echo.

REM Check Ollama (optional)
echo [3/4] Checking Ollama...

curl -s http://localhost:11434/api/version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo Ollama is not running (this is OK for now).
    echo Before using ASCAES, please:
    echo 1. Install Ollama from https://ollama.ai/
    echo 2. Start Ollama: ollama serve
    echo 3. Install models:
    echo    ollama pull deepseek-r1:1.5b
    echo    ollama pull nomic-embed-text
    echo.
) else (
    echo Ollama is running - OK
)

echo.

REM Final check
echo [4/4] Final verification...

if not exist "backend-python\app\main.py" (
    echo ERROR: Backend files missing
    pause
    exit /b 1
)

if not exist "frontend-react\src\App.tsx" (
    echo ERROR: Frontend files missing
    pause
    exit /b 1
)

echo File structure OK
echo.

echo ========================================
echo   Setup Complete!
echo ========================================
echo.
echo ✅ All dependencies installed
echo ✅ System verified
echo.
echo Next steps:
echo 1. Make sure Ollama is running: ollama serve
echo 2. Install models (if not done):
echo    ollama pull deepseek-r1:1.5b
echo    ollama pull nomic-embed-text
echo 3. Run ASCAES: run-ascaes.bat
echo.
echo Ready to generate academic documents!
echo.
pause
