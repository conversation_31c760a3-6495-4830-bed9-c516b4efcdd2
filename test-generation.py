#!/usr/bin/env python3
"""
Test the improved generation system
"""

import requests
import time

def test_generation():
    """Test the generation endpoint"""
    print("🔍 Testing Improved Generation System...")
    print("=" * 50)
    
    try:
        print("Testing backend connection...")
        response = requests.get("http://127.0.0.1:8000/api/v1/models/check-connection", timeout=10)
        print(f"Connection Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Connection: {data.get('status')}")
            
            print("\nTesting generation...")
            response = requests.post(
                "http://127.0.0.1:8000/api/v1/models/test-generate",
                timeout=120
            )
            
            print(f"Generation Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Response: {result}")
                
                if result.get("status") == "success":
                    content = result.get("response", "")
                    print(f"\n✅ SUCCESS!")
                    print(f"Generated Content: {content}")
                    print(f"Word Count: {len(content.split())} words")
                    
                    # Check for reasoning artifacts
                    if "<think>" in content.lower() or "thinking" in content.lower():
                        print("⚠️ Warning: Reasoning content detected")
                    else:
                        print("✅ Content is clean")
                        
                    return True
                else:
                    print(f"❌ Generation failed: {result.get('message')}")
                    return False
            else:
                print(f"❌ HTTP Error: {response.text}")
                return False
        else:
            print("❌ Backend connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == "__main__":
    success = test_generation()
    print("\n" + "=" * 50)
    if success:
        print("🎉 Generation system is working!")
    else:
        print("❌ Generation system failed")
