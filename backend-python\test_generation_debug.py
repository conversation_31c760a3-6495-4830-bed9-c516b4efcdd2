#!/usr/bin/env python3
"""
Test script to debug generation issues
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.database import SessionLocal
from app.core.generation_service import GenerationService
from app.db.models import Document

async def test_generation():
    """Test the generation service directly"""
    
    print("🧪 Testing generation service...")
    
    # Create database session
    db = SessionLocal()
    
    try:
        # Get the latest document
        document = db.query(Document).order_by(Document.id.desc()).first()
        if not document:
            print("❌ No documents found")
            return
        
        print(f"📄 Found document: {document.title} (ID: {document.id})")
        
        # Create generation service
        generation_service = GenerationService()
        
        # Test outline generation first
        print("📋 Testing outline generation...")
        try:
            chapter_titles = await generation_service.generate_chapter_outline(document)
            print(f"✅ Generated {len(chapter_titles)} chapter titles:")
            for i, title in enumerate(chapter_titles, 1):
                print(f"  {i}. {title}")
        except Exception as e:
            print(f"❌ Outline generation failed: {e}")
            return
        
        # Test single chapter generation
        print("\n📝 Testing single chapter generation...")
        try:
            prompt = generation_service.construct_prompt(document, chapter_titles[0], 1)
            print(f"📝 Prompt length: {len(prompt)} characters")
            
            content = await generation_service.call_ollama_generate_with_fallback(prompt)
            print(f"✅ Generated content ({len(content)} characters):")
            print(f"Preview: {content[:200]}...")
            
        except Exception as e:
            print(f"❌ Chapter generation failed: {e}")
            
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_generation())
