@echo off
echo Installing ASCAES - Academic Document Generation Specialist
echo.

echo Step 1: Installing Node.js dependencies...
call npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install Node.js dependencies
    pause
    exit /b 1
)

echo.
echo Step 2: Installing frontend dependencies...
cd frontend-react
call npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

echo.
echo Step 3: Installing Python dependencies...
cd backend-python
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo Error: Failed to install Python dependencies
    echo Make sure Python 3.8+ is installed and in your PATH
    pause
    exit /b 1
)
cd ..

echo.
echo Installation completed successfully!
echo.
echo Next steps:
echo 1. Make sure Ollama is installed and running
echo 2. Install required models: ollama pull deepseek-r1:latest
echo 3. Install embedding model: ollama pull nomic-embed-text
echo 4. Run the application: npm run dev
echo.
pause
