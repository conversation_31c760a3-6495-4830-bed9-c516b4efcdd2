"""
AI Logic Protocols - The Core IP
Contains all writing style definitions, cultural inflections, and AI protocols
"""

from app.db.models import AcademicFormattingSettings

# Detailed Writing Style Definitions
WRITING_STYLE_DEFINITIONS = {
    "Analytical": {
        "PROMPT_KEY": "ANALYZ<PERSON>", 
        "DESCRIPTION": "Adopt the persona of a critical thinker. Your goal is to dissect a topic into its core components, revealing the underlying logic, causes, and effects. Use precise, objective language and structure your argument with a clear thesis, systematic evidence, and a concluding synthesis. Focus on the 'why' and 'how'."
    },
    "Instructional": {
        "PROMPT_KEY": "TEACH", 
        "DESCRIPTION": "Adopt the persona of an expert instructor. Your goal is to teach a practical skill or concept. Use clear, direct, and encouraging language. Structure the content with a stated objective, followed by sequential, numbered steps with concrete examples. Define all jargon."
    },
    "Reporting": {
        "PROMPT_KEY": "REPORT", 
        "DESCRIPTION": "Adopt the persona of an investigative journalist or researcher. Your goal is to present verified, factual information with absolute neutrality. The tone is formal and data-driven. Structure with an executive summary, followed by clearly headed sections and bullet points. Focus on 'what, where, when'."
    },
    "Argumentative / Persuasive": {
        "PROMPT_KEY": "PERSUADE", 
        "DESCRIPTION": "Adopt the persona of a passionate advocate or debater. Your goal is to champion a specific viewpoint. The tone is assertive and confident, leveraging rhetorical devices and emotional resonance. Structure with a powerful thesis, followed by supporting evidence, pre-emptive refutation of counter-arguments, and a strong call to action."
    },
    "Exploratory / Reflective": {
        "PROMPT_KEY": "REFLECT", 
        "DESCRIPTION": "Adopt the persona of a philosopher or diarist. Your goal is to contemplate the deeper, often abstract or personal, meaning of a subject. The tone is introspective and questioning. The structure is often a journey of discovery, posing open-ended questions rather than providing firm answers."
    },
    "Descriptive": {
        "PROMPT_KEY": "DESCRIBE", 
        "DESCRIPTION": "Adopt the persona of a technical illustrator or architect. Your goal is to paint a vivid, multi-sensory, and technically precise picture of a subject. Move from a general overview to granular, specific details. Use concrete nouns and precise, evocative adjectives."
    },
    "Narrative": {
        "PROMPT_KEY": "NARRATE", 
        "DESCRIPTION": "Adopt the persona of a historian or storyteller. Your goal is to tell a compelling story about the topic's development over time. The tone is engaging and chronological. Structure the content using a classic narrative arc (setup, confrontation, resolution)."
    },
    "Schematic / Referential": {
        "PROMPT_KEY": "SUMMARIZE", 
        "DESCRIPTION": "Adopt the persona of a technical writer creating a reference manual. The goal is high-density information transfer for quick look-up. The tone is terse, factual, and utilitarian. Use heavy formatting like headings, lists, and bold keywords. Prefer concise phrases over long prose."
    }
}

# Detailed Cultural Inflection Definitions
CULTURAL_INFLECTION_DEFINITIONS = {
    "American": {
        "RHETORICAL_STYLE": "Direct, pragmatic, and solution-oriented. Arguments are built with concrete examples and practical applications. Values efficiency and actionable outcomes.",
        "TONE": "Confident, optimistic, and informal. Uses accessible language and avoids excessive formality. Emphasizes individual achievement and innovation.",
        "KEY_VALUES_EMPHASIZED": "Innovation, individual responsibility, practical results, entrepreneurship, and democratic ideals."
    },
    "Russian": {
        "RHETORICAL_STYLE": "Philosophical and systematic. Arguments are built with theoretical foundations and historical context. Values intellectual depth and comprehensive analysis.",
        "TONE": "Formal, contemplative, and sometimes melancholic. Uses complex sentence structures and abstract concepts. Emphasizes collective experience and historical consciousness.",
        "KEY_VALUES_EMPHASIZED": "Intellectual tradition, historical awareness, collective responsibility, spiritual depth, and systematic thinking."
    },
    "German": {
        "RHETORICAL_STYLE": "Methodical and thorough. Arguments are built with logical progression and detailed evidence. Values precision and systematic approach.",
        "TONE": "Formal, precise, and authoritative. Uses technical terminology and structured presentation. Emphasizes thoroughness and accuracy.",
        "KEY_VALUES_EMPHASIZED": "Precision, systematic methodology, technical excellence, thoroughness, and intellectual rigor."
    },
    "Japanese": {
        "RHETORICAL_STYLE": "Subtle and contextual. Arguments are built with implicit understanding and gradual revelation. Values harmony and consensus-building.",
        "TONE": "Respectful, modest, and indirect. Uses nuanced language and avoids direct confrontation. Emphasizes group harmony and continuous improvement.",
        "KEY_VALUES_EMPHASIZED": "Harmony, continuous improvement, respect for tradition, collective consensus, and attention to detail."
    },
    "French": {
        "RHETORICAL_STYLE": "Elegant and intellectual. Arguments are built with philosophical reasoning and cultural references. Values sophistication and theoretical depth.",
        "TONE": "Sophisticated, analytical, and sometimes skeptical. Uses refined language and cultural allusions. Emphasizes intellectual discourse and aesthetic quality.",
        "KEY_VALUES_EMPHASIZED": "Intellectual sophistication, cultural refinement, philosophical depth, aesthetic appreciation, and critical thinking."
    },
    "Italian": {
        "RHETORICAL_STYLE": "Eloquent and humanistic. Arguments are built with historical and philosophical context, often referencing classical tradition. Values rhetorical flair ('sprezzatura').",
        "TONE": "Intellectual, passionate, and formal but can be more personal than German/Russian styles. Aims for elegance and sophistication.",
        "KEY_VALUES_EMPHASIZED": "Historical consciousness, aesthetic quality, intellectual tradition, eloquence, and the significance of 'genius' or individual brilliance."
    }
}

# Humanization Protocol
HUMANIZATION_PROTOCOL = """
### HUMANIZATION PROTOCOL ###
To enhance realism, you must adhere to the following stylistic guidelines:
1.  **Vary Sentence Structure:** Actively alternate between simple, compound, and complex sentences. Avoid starting consecutive sentences with the same word or phrase.
2.  **Use Rhetorical Devices:** Subtly incorporate rhetorical questions, analogies, and metaphors where appropriate for the writing style.
3.  **Avoid AI Hallmarks:** Do not use phrases like "In conclusion," "Furthermore," "It is important to note," or "delve." Be direct.
4.  **Show, Don't Just Tell:** Instead of stating a fact, describe the evidence that leads to it when the style allows.
"""

# Grammatical Correction Protocol
GRAMMATICAL_CORRECTION_PROTOCOL = """
You are a meticulous, world-class editor. Your only task is to correct the following text for any and all grammatical errors, spelling mistakes, punctuation errors, and awkward phrasing. You must not alter the core meaning or style of the text. Return only the corrected text, with no preamble or explanation.
"""

def get_word_count_estimate(pages: int, doc_type: str, spacing: str) -> int:
    """Calculates an estimated word count based on document type and spacing."""
    base_words_per_page = 250  # The academic double-spaced standard

    # Adjust for spacing
    multiplier = 1.0 if spacing == 'Double' else 1.9  # Single-spacing is almost double

    # Adjust for document type (books and reports are denser)
    if doc_type == 'Book':
        type_multiplier = 1.1
    elif doc_type == 'Report':
        type_multiplier = 1.05
    else:  # Academic Paper
        type_multiplier = 1.0

    return int(pages * base_words_per_page * multiplier * type_multiplier)

def get_academic_formatting_protocol(settings: AcademicFormattingSettings) -> str:
    """Generates the formatting part of the prompt based on user settings."""
    if not settings.enable_academic_formatting:
        return ""

    formatting_rules = []

    # Header information
    if settings.student_name:
        formatting_rules.append(f"Student Name: {settings.student_name}")
    if settings.course_name:
        formatting_rules.append(f"Course: {settings.course_name}")
    if settings.instructor_name:
        formatting_rules.append(f"Instructor: {settings.instructor_name}")
    if settings.due_date:
        formatting_rules.append(f"Due Date: {settings.due_date}")

    # Citation style
    citation_info = ""
    if settings.citation_style:
        citation_info = f"Use {settings.citation_style} citation style throughout the document."

    formatting_protocol = f"""
### ACADEMIC FORMATTING PROTOCOL ###
Apply the following academic formatting requirements:

HEADER INFORMATION:
{chr(10).join(formatting_rules)}

CITATION REQUIREMENTS:
{citation_info}

FORMATTING STANDARDS:
- Use proper academic structure with clear headings and subheadings
- Include appropriate in-text citations where relevant
- Maintain consistent formatting throughout
- Use formal academic tone and language
- Include proper paragraph structure and transitions
"""

    return formatting_protocol
