import React, { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { 
  ArrowLeft, 
  Play, 
  Download, 
  CheckCircle,
  Clock,
  AlertCircle,
  RefreshCw,
  FileText,
  BookOpen
} from 'lucide-react'
import { useAppStore } from '../store/useAppStore'

const DocumentViewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const {
    currentDocument,
    currentChapters,
    generationStatus,
    isLoading,
    error,
    fetchDocuments,
    setCurrentDocument,
    fetchDocumentChapters,
    startGeneration,
    fetchGenerationStatus,
    checkGrammar,
    setError
  } = useAppStore()
  
  const [selectedChapter, setSelectedChapter] = useState<number | null>(null)
  const [isCheckingGrammar, setIsCheckingGrammar] = useState(false)
  const [correctedText, setCorrectedText] = useState<string>('')
  
  const documentId = parseInt(id || '0')
  
  useEffect(() => {
    if (documentId) {
      console.log('DocumentViewPage: Loading document ID:', documentId)
      // Find document in store or fetch all documents
      fetchDocuments().then(() => {
        console.log('DocumentViewPage: Documents fetched, now fetching chapters and status')
        fetchDocumentChapters(documentId)
        fetchGenerationStatus(documentId)
      }).catch(error => {
        console.error('DocumentViewPage: Error fetching documents:', error)
        setError(`Failed to load documents: ${error.message}`)
      })
    }
  }, [documentId, fetchDocuments, fetchDocumentChapters, fetchGenerationStatus])
  
  // Set current document from documents list
  useEffect(() => {
    const { documents } = useAppStore.getState()
    const doc = documents.find(d => d.id === documentId)
    if (doc) {
      setCurrentDocument(doc)
    }
  }, [documentId, setCurrentDocument])
  
  // Poll generation status if document is generating
  useEffect(() => {
    let interval: NodeJS.Timeout
    
    if (currentDocument?.status === 'generating') {
      interval = setInterval(() => {
        fetchGenerationStatus(documentId)
      }, 3000) // Poll every 3 seconds
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [currentDocument?.status, documentId, fetchGenerationStatus])
  
  const handleStartGeneration = async () => {
    if (documentId) {
      console.log('DocumentViewPage: Starting generation for document:', documentId)
      try {
        await startGeneration(documentId)
        console.log('DocumentViewPage: Generation started successfully')
        // Start polling for status
        fetchGenerationStatus(documentId)
      } catch (error) {
        console.error('DocumentViewPage: Error starting generation:', error)
        setError(`Failed to start generation: ${error.message}`)
      }
    }
  }
  
  const handleGrammarCheck = async (text: string) => {
    if (!text.trim()) return
    
    setIsCheckingGrammar(true)
    try {
      const corrected = await checkGrammar(text)
      setCorrectedText(corrected)
    } catch (error) {
      setError('Failed to check grammar')
    } finally {
      setIsCheckingGrammar(false)
    }
  }
  
  const handleExportDocument = async () => {
    try {
      const response = await fetch(`http://127.0.0.1:8000/api/v1/documents/${documentId}/export`)
      if (!response.ok) throw new Error('Failed to export document')
      
      const data = await response.json()
      
      // Create and download file
      const blob = new Blob([data.full_text], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${data.title}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      setError('Failed to export document')
    }
  }
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-dark-success" />
      case 'generating':
        return <Clock className="w-5 h-5 text-dark-warning animate-pulse" />
      case 'error':
        return <AlertCircle className="w-5 h-5 text-dark-error" />
      default:
        return <FileText className="w-5 h-5 text-dark-text-secondary" />
    }
  }
  
  const selectedChapterData = selectedChapter 
    ? currentChapters.find(ch => ch.id === selectedChapter)
    : null
  
  if (!currentDocument) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <FileText className="w-16 h-16 text-dark-text-secondary mx-auto mb-4" />
          <p className="text-dark-text-secondary">Document not found</p>
          <button
            onClick={() => navigate('/documents')}
            className="btn-primary mt-4"
          >
            Back to Documents
          </button>
        </div>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      {/* Error Display */}
      {error && (
        <div className="bg-dark-error/10 border border-dark-error/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-dark-error text-sm">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-dark-error hover:text-dark-error/80"
            >
              ×
            </button>
          </div>
        </div>
      )}
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/documents')}
            className="btn-secondary flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back</span>
          </button>
          
          <div>
            <h1 className="text-2xl font-bold text-dark-text">{currentDocument.title}</h1>
            <div className="flex items-center space-x-4 mt-1">
              <span className="text-sm text-dark-text-secondary">
                {currentDocument.document_type} • {currentDocument.pages} pages
              </span>
              <div className="flex items-center space-x-2">
                {getStatusIcon(currentDocument.status)}
                <span className="text-sm text-dark-text-secondary capitalize">
                  {currentDocument.status}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          {currentDocument.status === 'pending' && (
            <button
              onClick={handleStartGeneration}
              disabled={isLoading}
              className="btn-primary flex items-center space-x-2"
            >
              <Play className="w-4 h-4" />
              <span>Start Generation</span>
            </button>
          )}
          
          {currentDocument.status === 'completed' && (
            <button
              onClick={handleExportDocument}
              className="btn-secondary flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </button>
          )}
          
          <button
            onClick={() => fetchGenerationStatus(documentId)}
            className="btn-secondary"
            title="Refresh status"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      {/* Progress Bar */}
      {currentDocument.status === 'generating' && (
        <div className="card">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-dark-text">Generation Progress</span>
            <span className="text-sm text-dark-text-secondary">
              {Math.round(currentDocument.progress)}%
            </span>
          </div>
          <div className="w-full bg-dark-border rounded-full h-3">
            <div 
              className="bg-dark-accent h-3 rounded-full transition-all duration-500"
              style={{ width: `${currentDocument.progress}%` }}
            />
          </div>
          {generationStatus && (
            <p className="text-sm text-dark-text-secondary mt-2">
              {generationStatus.chapters.filter(ch => ch.status === 'completed').length} of {generationStatus.total_chapters} chapters completed
            </p>
          )}
        </div>
      )}
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Chapters List */}
        <div className="lg:col-span-1">
          <div className="card">
            <h3 className="text-lg font-semibold text-dark-text mb-4 flex items-center space-x-2">
              <BookOpen className="w-5 h-5" />
              <span>Chapters</span>
            </h3>
            
            <div className="space-y-2">
              {currentChapters.length > 0 ? (
                currentChapters.map((chapter) => (
                  <button
                    key={chapter.id}
                    onClick={() => setSelectedChapter(chapter.id)}
                    className={`w-full text-left p-3 rounded-lg border transition-colors duration-200 ${
                      selectedChapter === chapter.id
                        ? 'border-dark-accent bg-dark-accent/10'
                        : 'border-dark-border hover:border-dark-accent/50 hover:bg-dark-border/30'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-dark-text">
                        Chapter {chapter.chapter_number}
                      </span>
                      {getStatusIcon(chapter.status)}
                    </div>
                    <p className="text-sm text-dark-text-secondary truncate">
                      {chapter.title}
                    </p>
                    {chapter.word_count > 0 && (
                      <p className="text-xs text-dark-text-secondary mt-1">
                        {chapter.word_count} words
                      </p>
                    )}
                  </button>
                ))
              ) : (
                <div className="text-center py-8">
                  <BookOpen className="w-12 h-12 text-dark-text-secondary mx-auto mb-2" />
                  <p className="text-sm text-dark-text-secondary">
                    No chapters yet
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Chapter Content */}
        <div className="lg:col-span-2">
          <div className="card">
            {selectedChapterData ? (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-dark-text">
                    Chapter {selectedChapterData.chapter_number}: {selectedChapterData.title}
                  </h3>
                  
                  {currentDocument.enable_grammar_check && selectedChapterData.content && (
                    <button
                      onClick={() => handleGrammarCheck(selectedChapterData.content!)}
                      disabled={isCheckingGrammar}
                      className="btn-secondary flex items-center space-x-2"
                    >
                      {isCheckingGrammar ? (
                        <RefreshCw className="w-4 h-4 animate-spin" />
                      ) : (
                        <CheckCircle className="w-4 h-4" />
                      )}
                      <span>Check Grammar</span>
                    </button>
                  )}
                </div>
                
                {selectedChapterData.content ? (
                  <div className="space-y-4">
                    <div className="bg-dark-bg border border-dark-border rounded-lg p-4">
                      <pre className="whitespace-pre-wrap text-sm text-dark-text font-sans leading-relaxed">
                        {selectedChapterData.content}
                      </pre>
                    </div>
                    
                    {correctedText && (
                      <div>
                        <h4 className="text-sm font-medium text-dark-text mb-2">Grammar Corrected Version:</h4>
                        <div className="bg-dark-success/5 border border-dark-success/20 rounded-lg p-4">
                          <pre className="whitespace-pre-wrap text-sm text-dark-text font-sans leading-relaxed">
                            {correctedText}
                          </pre>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Clock className="w-12 h-12 text-dark-text-secondary mx-auto mb-4" />
                    <p className="text-dark-text-secondary">
                      {selectedChapterData.status === 'generating' 
                        ? 'Chapter is being generated...'
                        : 'Chapter content not available yet'
                      }
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <FileText className="w-12 h-12 text-dark-text-secondary mx-auto mb-4" />
                <p className="text-dark-text-secondary">
                  Select a chapter to view its content
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default DocumentViewPage
