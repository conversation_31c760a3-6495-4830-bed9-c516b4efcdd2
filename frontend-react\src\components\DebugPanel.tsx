import React, { useState, useEffect } from 'react'
import { AlertCircle, CheckCircle, RefreshCw, X } from 'lucide-react'
import { useAppStore } from '../store/useAppStore'

interface DebugPanelProps {
  isOpen: boolean
  onClose: () => void
}

const DebugPanel: React.FC<DebugPanelProps> = ({ isOpen, onClose }) => {
  const { testConnection, error } = useAppStore()
  const [connectionStatus, setConnectionStatus] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  const runConnectionTest = async () => {
    setIsLoading(true)
    try {
      const results = await testConnection()
      setConnectionStatus(results)
    } catch (error) {
      console.error('Connection test failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (isOpen) {
      runConnectionTest()
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-dark-panel border border-dark-border rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-dark-text">Debug Panel</h2>
          <button
            onClick={onClose}
            className="text-dark-text-secondary hover:text-dark-text"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Connection Status */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-medium text-dark-text">Connection Status</h3>
            <button
              onClick={runConnectionTest}
              disabled={isLoading}
              className="btn-secondary flex items-center space-x-2"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Test</span>
            </button>
          </div>

          {connectionStatus && (
            <div className="space-y-3">
              {/* Backend Status */}
              <div className="flex items-center space-x-3 p-3 bg-dark-bg rounded border border-dark-border">
                {connectionStatus.backend ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-red-500" />
                )}
                <div className="flex-1">
                  <div className="font-medium text-dark-text">Backend API</div>
                  <div className="text-sm text-dark-text-secondary">
                    {connectionStatus.details.backend_url}
                  </div>
                  {connectionStatus.details.backend_error && (
                    <div className="text-sm text-red-400 mt-1">
                      {connectionStatus.details.backend_error}
                    </div>
                  )}
                </div>
              </div>

              {/* Ollama Status */}
              <div className="flex items-center space-x-3 p-3 bg-dark-bg rounded border border-dark-border">
                {connectionStatus.ollama ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-red-500" />
                )}
                <div className="flex-1">
                  <div className="font-medium text-dark-text">Ollama Service</div>
                  <div className="text-sm text-dark-text-secondary">
                    {connectionStatus.details.ollama_url || 'Unknown'}
                  </div>
                  {connectionStatus.details.ollama_error && (
                    <div className="text-sm text-red-400 mt-1">
                      {connectionStatus.details.ollama_error}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Current Error */}
        {error && (
          <div className="mb-6">
            <h3 className="text-lg font-medium text-dark-text mb-3">Current Error</h3>
            <div className="p-3 bg-red-900/20 border border-red-500/30 rounded text-red-400">
              {error}
            </div>
          </div>
        )}

        {/* Console Logs */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-dark-text mb-3">Debug Instructions</h3>
          <div className="p-3 bg-dark-bg rounded border border-dark-border text-sm text-dark-text-secondary">
            <p className="mb-2">To see detailed debug information:</p>
            <ol className="list-decimal list-inside space-y-1">
              <li>Open browser Developer Tools (F12)</li>
              <li>Go to Console tab</li>
              <li>Look for messages starting with "Store:" or "ChatPage:"</li>
              <li>Try sending a message in chat to see the full flow</li>
            </ol>
          </div>
        </div>

        {/* Quick Tests */}
        <div>
          <h3 className="text-lg font-medium text-dark-text mb-3">Quick Tests</h3>
          <div className="space-y-2">
            <button
              onClick={async () => {
                console.log('Testing backend health...')
                try {
                  const response = await fetch('http://127.0.0.1:8002/health')
                  const data = await response.json()
                  console.log('Backend health:', data)
                  alert(`✅ Backend Health: ${data.message || 'OK'}`)
                } catch (error) {
                  console.error('Backend health failed:', error)
                  alert(`❌ Backend Health Failed: ${error.message}`)
                }
              }}
              className="btn-secondary w-full text-left"
            >
              Test Backend Health
            </button>

            <button
              onClick={async () => {
                console.log('Testing documents endpoint...')
                try {
                  const response = await fetch('http://127.0.0.1:8002/api/v1/documents')
                  const data = await response.json()
                  console.log('Documents:', data)
                  alert(`✅ Documents: Found ${data.length} documents`)
                } catch (error) {
                  console.error('Documents failed:', error)
                  alert(`❌ Documents Failed: ${error.message}`)
                }
              }}
              className="btn-secondary w-full text-left"
            >
              Test Documents Endpoint
            </button>

            <button
              onClick={async () => {
                console.log('Testing models status...')
                try {
                  const response = await fetch('http://127.0.0.1:8002/api/v1/models/status')
                  const data = await response.json()
                  console.log('Models status:', data)
                  alert(`✅ Models Status: ${data.connected ? 'Connected' : 'Disconnected'} - ${data.message || data.error}`)
                } catch (error) {
                  console.error('Models status failed:', error)
                  alert(`❌ Models Status Failed: ${error.message}`)
                }
              }}
              className="btn-secondary w-full text-left"
            >
              Test Models Status
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DebugPanel
