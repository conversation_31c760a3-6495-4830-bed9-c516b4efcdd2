import React, { useState, useEffect } from 'react'
import { <PERSON>, Refresh<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, FileText, <PERSON> } from 'lucide-react'

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState({
    // Generation Parameters
    temperature: 0.7,
    topP: 0.9,
    topK: 40,
    maxTokensPerPage: 800,
    repeatPenalty: 1.1,
    contextWindow: 4096,
    
    // Academic Settings
    defaultCitationStyle: 'APA',
    enableGrammarCheck: true,
    enablePlagiarismCheck: false,
    academicTone: 'Formal',
    
    // Performance Settings
    modelKeepAlive: '10m',
    connectionTimeout: 300,
    retryAttempts: 3,
    batchSize: 1,
    
    // UI Settings
    autoSave: true,
    showWordCount: true,
    enablePreview: true,
    darkMode: true
  })
  
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking')
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  useEffect(() => {
    checkConnection()
    loadSettings()
  }, [])

  const checkConnection = async () => {
    setConnectionStatus('checking')
    try {
      const response = await fetch('http://127.0.0.1:8002/api/v1/models/check-connection')
      const result = await response.json()
      setConnectionStatus(result.status === 'connected' ? 'connected' : 'disconnected')
    } catch (error) {
      setConnectionStatus('disconnected')
    }
  }

  const loadSettings = () => {
    const saved = localStorage.getItem('ascaes-settings')
    if (saved) {
      setSettings({ ...settings, ...JSON.parse(saved) })
    }
  }

  const saveSettings = async () => {
    setIsSaving(true)
    try {
      localStorage.setItem('ascaes-settings', JSON.stringify(settings))
      setLastSaved(new Date())
    } catch (error) {
      console.error('Failed to save settings:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const resetToDefaults = () => {
    setSettings({
      temperature: 0.7,
      topP: 0.9,
      topK: 40,
      maxTokensPerPage: 800,
      repeatPenalty: 1.1,
      contextWindow: 4096,
      defaultCitationStyle: 'APA',
      enableGrammarCheck: true,
      enablePlagiarismCheck: false,
      academicTone: 'Formal',
      modelKeepAlive: '10m',
      connectionTimeout: 300,
      retryAttempts: 3,
      batchSize: 1,
      autoSave: true,
      showWordCount: true,
      enablePreview: true,
      darkMode: true
    })
  }

  const handleInputChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  return (
    <div className="flex-1 p-6 bg-dark-bg overflow-y-auto">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-dark-text">Settings</h1>
            <p className="text-dark-text-secondary">Configure application parameters for optimal academic document generation</p>
          </div>
          
          <div className="flex items-center space-x-3">
            {lastSaved && (
              <span className="text-sm text-dark-text-secondary">
                Last saved: {lastSaved.toLocaleTimeString()}
              </span>
            )}
            <button
              onClick={resetToDefaults}
              className="btn-secondary flex items-center space-x-2"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Reset</span>
            </button>
            <button
              onClick={saveSettings}
              disabled={isSaving}
              className="btn-primary flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>{isSaving ? 'Saving...' : 'Save Settings'}</span>
            </button>
          </div>
        </div>

        {/* Connection Status */}
        <div className="bg-dark-panel rounded-lg p-4 border border-dark-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Brain className="w-5 h-5 text-dark-accent" />
              <div>
                <h3 className="font-semibold text-dark-text">Ollama Connection</h3>
                <p className="text-sm text-dark-text-secondary">AI model service status</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className={`flex items-center space-x-2 ${
                connectionStatus === 'connected' ? 'text-green-400' :
                connectionStatus === 'disconnected' ? 'text-red-400' : 'text-yellow-400'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  connectionStatus === 'connected' ? 'bg-green-400' :
                  connectionStatus === 'disconnected' ? 'bg-red-400' : 'bg-yellow-400'
                }`}></div>
                <span className="text-sm font-medium">
                  {connectionStatus === 'connected' ? 'Connected' :
                   connectionStatus === 'disconnected' ? 'Disconnected' : 'Checking...'}
                </span>
              </div>
              <button
                onClick={checkConnection}
                className="btn-secondary p-2"
                title="Refresh Connection"
              >
                <RefreshCw className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Generation Parameters */}
        <div className="bg-dark-panel rounded-lg p-6 border border-dark-border">
          <div className="flex items-center space-x-3 mb-6">
            <Sliders className="w-5 h-5 text-dark-accent" />
            <h2 className="text-xl font-semibold text-dark-text">Generation Parameters</h2>
          </div>
          
          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-dark-text mb-2">
                Temperature ({settings.temperature})
              </label>
              <input
                type="range"
                min="0.1"
                max="1.0"
                step="0.1"
                value={settings.temperature}
                onChange={(e) => handleInputChange('temperature', parseFloat(e.target.value))}
                className="w-full h-2 bg-dark-border rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-dark-text-secondary mt-1">
                <span>Conservative</span>
                <span>Creative</span>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-dark-text mb-2">
                Max Tokens per Page
              </label>
              <input
                type="number"
                min="400"
                max="2000"
                step="100"
                value={settings.maxTokensPerPage}
                onChange={(e) => handleInputChange('maxTokensPerPage', parseInt(e.target.value))}
                className="input-field w-full"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-dark-text mb-2">
                Top P ({settings.topP})
              </label>
              <input
                type="range"
                min="0.1"
                max="1.0"
                step="0.1"
                value={settings.topP}
                onChange={(e) => handleInputChange('topP', parseFloat(e.target.value))}
                className="w-full h-2 bg-dark-border rounded-lg appearance-none cursor-pointer slider"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-dark-text mb-2">
                Top K
              </label>
              <input
                type="number"
                min="10"
                max="100"
                step="10"
                value={settings.topK}
                onChange={(e) => handleInputChange('topK', parseInt(e.target.value))}
                className="input-field w-full"
              />
            </div>
          </div>
        </div>

        {/* Academic Settings */}
        <div className="bg-dark-panel rounded-lg p-6 border border-dark-border">
          <div className="flex items-center space-x-3 mb-6">
            <FileText className="w-5 h-5 text-dark-accent" />
            <h2 className="text-xl font-semibold text-dark-text">Academic Settings</h2>
          </div>
          
          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-dark-text mb-2">
                Default Citation Style
              </label>
              <select
                value={settings.defaultCitationStyle}
                onChange={(e) => handleInputChange('defaultCitationStyle', e.target.value)}
                className="select-field w-full"
              >
                <option value="APA">APA</option>
                <option value="MLA">MLA</option>
                <option value="Chicago">Chicago</option>
                <option value="Harvard">Harvard</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-dark-text mb-2">
                Academic Tone
              </label>
              <select
                value={settings.academicTone}
                onChange={(e) => handleInputChange('academicTone', e.target.value)}
                className="select-field w-full"
              >
                <option value="Formal">Formal</option>
                <option value="Semi-formal">Semi-formal</option>
                <option value="Conversational">Conversational</option>
              </select>
            </div>
          </div>
          
          <div className="mt-6 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-dark-text">Grammar Check</label>
                <p className="text-xs text-dark-text-secondary">Enable automatic grammar validation</p>
              </div>
              <button
                type="button"
                onClick={() => handleInputChange('enableGrammarCheck', !settings.enableGrammarCheck)}
                className={`toggle-switch ${settings.enableGrammarCheck ? 'enabled' : 'disabled'}`}
              >
                <span className={`toggle-thumb ${settings.enableGrammarCheck ? 'enabled' : 'disabled'}`} />
              </button>
            </div>
          </div>
        </div>

        {/* Performance Settings */}
        <div className="bg-dark-panel rounded-lg p-6 border border-dark-border">
          <div className="flex items-center space-x-3 mb-6">
            <Zap className="w-5 h-5 text-dark-accent" />
            <h2 className="text-xl font-semibold text-dark-text">Performance Settings</h2>
          </div>
          
          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-dark-text mb-2">
                Model Keep Alive
              </label>
              <select
                value={settings.modelKeepAlive}
                onChange={(e) => handleInputChange('modelKeepAlive', e.target.value)}
                className="select-field w-full"
              >
                <option value="5m">5 minutes</option>
                <option value="10m">10 minutes</option>
                <option value="30m">30 minutes</option>
                <option value="1h">1 hour</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-dark-text mb-2">
                Connection Timeout (seconds)
              </label>
              <input
                type="number"
                min="60"
                max="600"
                step="30"
                value={settings.connectionTimeout}
                onChange={(e) => handleInputChange('connectionTimeout', parseInt(e.target.value))}
                className="input-field w-full"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SettingsPage
