import React, { useEffect, useState } from 'react'
import { 
  Settings, 
  Server, 
  Download,
  CheckCircle,
  AlertCircle,
  RefreshCw
} from 'lucide-react'

interface OllamaModel {
  name: string
  size: number
  modified_at: string
  digest: string
}

interface ConnectionStatus {
  status: 'connected' | 'disconnected' | 'error'
  version?: string
  message: string
}

const SettingsPage: React.FC = () => {
  const [models, setModels] = useState<OllamaModel[]>([])
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const API_BASE_URL = 'http://127.0.0.1:8000/api/v1'
  
  useEffect(() => {
    checkConnection()
    fetchModels()
  }, [])
  
  const checkConnection = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/models/check-connection`)
      const data = await response.json()
      setConnectionStatus(data)
    } catch (error) {
      setConnectionStatus({
        status: 'error',
        message: 'Failed to check connection'
      })
    }
  }
  
  const fetchModels = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`${API_BASE_URL}/models`)
      if (response.ok) {
        const data = await response.json()
        setModels(data.models || [])
      } else {
        setError('Failed to fetch models')
      }
    } catch (error) {
      setError('Error connecting to Ollama service')
    } finally {
      setIsLoading(false)
    }
  }
  
  const pullModel = async (modelName: string) => {
    setIsLoading(true)
    setError(null)
    try {
      const response = await fetch(`${API_BASE_URL}/models/pull?model_name=${modelName}`, {
        method: 'POST'
      })
      
      if (response.ok) {
        await fetchModels() // Refresh models list
      } else {
        setError(`Failed to pull model: ${modelName}`)
      }
    } catch (error) {
      setError(`Error pulling model: ${modelName}`)
    } finally {
      setIsLoading(false)
    }
  }
  
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  const getConnectionStatusIcon = () => {
    if (!connectionStatus) return <RefreshCw className="w-5 h-5 animate-spin" />
    
    switch (connectionStatus.status) {
      case 'connected':
        return <CheckCircle className="w-5 h-5 text-dark-success" />
      case 'disconnected':
      case 'error':
        return <AlertCircle className="w-5 h-5 text-dark-error" />
      default:
        return <RefreshCw className="w-5 h-5 animate-spin" />
    }
  }
  
  const recommendedModels = [
    {
      name: 'deepseek-r1:latest',
      description: 'Advanced reasoning model, excellent for academic writing',
      size: 'Large (~40GB)',
      recommended: true
    },
    {
      name: 'nomic-embed-text',
      description: 'High-quality text embedding model for RAG',
      size: 'Small (~1GB)',
      recommended: true
    },
    {
      name: 'llama3:8b',
      description: 'Smaller but capable model for faster generation',
      size: 'Medium (~8GB)',
      recommended: false
    }
  ]
  
  return (
    <div className="space-y-8">
      {/* Error Display */}
      {error && (
        <div className="bg-dark-error/10 border border-dark-error/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-dark-error text-sm">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-dark-error hover:text-dark-error/80"
            >
              ×
            </button>
          </div>
        </div>
      )}
      
      {/* Ollama Connection Status */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-dark-text flex items-center space-x-2">
            <Server className="w-5 h-5" />
            <span>Ollama Connection</span>
          </h3>
          
          <button
            onClick={checkConnection}
            className="btn-secondary"
            title="Refresh connection status"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
        
        <div className="flex items-center space-x-3">
          {getConnectionStatusIcon()}
          <div>
            <p className="text-dark-text font-medium">
              {connectionStatus?.status === 'connected' ? 'Connected' : 'Disconnected'}
            </p>
            <p className="text-sm text-dark-text-secondary">
              {connectionStatus?.message || 'Checking connection...'}
            </p>
            {connectionStatus?.version && (
              <p className="text-xs text-dark-text-secondary">
                Version: {connectionStatus.version}
              </p>
            )}
          </div>
        </div>
        
        {connectionStatus?.status !== 'connected' && (
          <div className="mt-4 p-4 bg-dark-warning/10 border border-dark-warning/20 rounded-lg">
            <p className="text-sm text-dark-warning">
              <strong>Note:</strong> Ollama must be running on localhost:11434 for ASCAES to function properly.
              Please ensure Ollama is installed and running.
            </p>
          </div>
        )}
      </div>
      
      {/* Installed Models */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-dark-text">Installed Models</h3>
          <button
            onClick={fetchModels}
            disabled={isLoading}
            className="btn-secondary flex items-center space-x-2"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
        
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin text-dark-accent" />
          </div>
        ) : models.length > 0 ? (
          <div className="space-y-3">
            {models.map((model) => (
              <div key={model.name} className="flex items-center justify-between p-3 bg-dark-bg border border-dark-border rounded-lg">
                <div>
                  <p className="font-medium text-dark-text font-mono">{model.name}</p>
                  <p className="text-sm text-dark-text-secondary">
                    Size: {formatBytes(model.size)}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-dark-success" />
                  <span className="text-sm text-dark-success">Installed</span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Server className="w-12 h-12 text-dark-text-secondary mx-auto mb-4" />
            <p className="text-dark-text-secondary">No models installed</p>
            <p className="text-sm text-dark-text-secondary mt-1">
              Install recommended models below to get started
            </p>
          </div>
        )}
      </div>
      
      {/* Recommended Models */}
      <div className="card">
        <h3 className="text-lg font-semibold text-dark-text mb-4">Recommended Models</h3>
        
        <div className="space-y-4">
          {recommendedModels.map((model) => {
            const isInstalled = models.some(m => m.name === model.name)
            
            return (
              <div key={model.name} className="flex items-center justify-between p-4 bg-dark-bg border border-dark-border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <p className="font-medium text-dark-text font-mono">{model.name}</p>
                    {model.recommended && (
                      <span className="text-xs bg-dark-accent/10 text-dark-accent px-2 py-1 rounded-full">
                        Recommended
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-dark-text-secondary mb-1">{model.description}</p>
                  <p className="text-xs text-dark-text-secondary">Size: {model.size}</p>
                </div>
                
                <div className="ml-4">
                  {isInstalled ? (
                    <div className="flex items-center space-x-2 text-dark-success">
                      <CheckCircle className="w-5 h-5" />
                      <span className="text-sm">Installed</span>
                    </div>
                  ) : (
                    <button
                      onClick={() => pullModel(model.name)}
                      disabled={isLoading}
                      className="btn-primary flex items-center space-x-2"
                    >
                      <Download className="w-4 h-4" />
                      <span>Install</span>
                    </button>
                  )}
                </div>
              </div>
            )
          })}
        </div>
        
        <div className="mt-6 p-4 bg-dark-accent/10 border border-dark-accent/20 rounded-lg">
          <p className="text-sm text-dark-accent">
            <strong>Note:</strong> Model installation may take several minutes depending on your internet connection.
            The deepseek-r1 model is particularly large but provides the best results for academic writing.
          </p>
        </div>
      </div>
      
      {/* Application Info */}
      <div className="card">
        <h3 className="text-lg font-semibold text-dark-text mb-4">Application Information</h3>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-dark-text-secondary">Version</span>
            <span className="text-dark-text font-mono">1.0.0</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-dark-text-secondary">Backend API</span>
            <span className="text-dark-text font-mono">http://127.0.0.1:8000</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-dark-text-secondary">Ollama API</span>
            <span className="text-dark-text font-mono">http://localhost:11434</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SettingsPage
