"""
RAG Service - Retrieval-Augmented Generation
Manages ChromaDB interactions for document memory and context retrieval
"""

import chromadb
from chromadb.config import Settings
import httpx
import json
import uuid
from typing import List, Dict, Any
import re
import os

class RAGService:
    def __init__(self):
        # Initialize ChromaDB client for local storage
        self.client = chromadb.PersistentClient(
            path="./chroma_db",
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # Create or get collection for document chunks
        self.collection = self.client.get_or_create_collection(
            name="document_chunks",
            metadata={"description": "Document chunks with embeddings for RAG"}
        )
        
        # Ollama API configuration
        self.ollama_base_url = "http://localhost:11434"
        self.embedding_model = "nomic-embed-text"
        
    async def get_embedding(self, text: str) -> List[float]:
        """Get embedding for text using Ollama embedding model"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.ollama_base_url}/api/embed",
                    json={
                        "model": self.embedding_model,
                        "input": text
                    },
                    timeout=30.0
                )
                response.raise_for_status()
                result = response.json()
                return result["embeddings"][0] if result.get("embeddings") else result.get("embedding", [])
        except Exception as e:
            print(f"Error getting embedding: {e}")
            # Return a dummy embedding if Ollama is not available
            return [0.0] * 768  # Standard embedding dimension
    
    def chunk_text(self, text: str, chunk_size: int = 500, overlap: int = 50) -> List[str]:
        """Split text into overlapping chunks for better context preservation"""
        if not text or len(text.strip()) == 0:
            return []
        
        # Split by paragraphs first
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            # If adding this paragraph would exceed chunk size, save current chunk
            if len(current_chunk) + len(paragraph) > chunk_size and current_chunk:
                chunks.append(current_chunk.strip())
                # Start new chunk with overlap from previous chunk
                if overlap > 0 and len(current_chunk) > overlap:
                    current_chunk = current_chunk[-overlap:] + " " + paragraph
                else:
                    current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
        
        # Add the last chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    async def add_text_to_memory(self, text: str, document_id: int, chapter_id: int, chapter_title: str):
        """Chunk text, get embeddings, and store in ChromaDB"""
        if not text or len(text.strip()) == 0:
            return
        
        chunks = self.chunk_text(text)
        
        for i, chunk in enumerate(chunks):
            if len(chunk.strip()) < 50:  # Skip very small chunks
                continue
                
            # Get embedding for chunk
            embedding = await self.get_embedding(chunk)
            
            # Create unique ID for chunk
            chunk_id = f"doc_{document_id}_ch_{chapter_id}_chunk_{i}"
            
            # Store in ChromaDB
            self.collection.add(
                embeddings=[embedding],
                documents=[chunk],
                metadatas=[{
                    "document_id": document_id,
                    "chapter_id": chapter_id,
                    "chapter_title": chapter_title,
                    "chunk_index": i,
                    "chunk_id": chunk_id
                }],
                ids=[chunk_id]
            )
    
    async def retrieve_relevant_context(self, query: str, document_id: int, n_results: int = 5) -> List[Dict[str, Any]]:
        """Retrieve most relevant chunks for a query from the same document"""
        if not query or len(query.strip()) == 0:
            return []
        
        try:
            # Get embedding for query
            query_embedding = await self.get_embedding(query)
            
            # Search in ChromaDB for relevant chunks from the same document
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where={"document_id": document_id}
            )
            
            # Format results
            context_chunks = []
            if results["documents"] and len(results["documents"]) > 0:
                for i in range(len(results["documents"][0])):
                    context_chunks.append({
                        "text": results["documents"][0][i],
                        "metadata": results["metadatas"][0][i],
                        "distance": results["distances"][0][i] if results["distances"] else 0.0
                    })
            
            return context_chunks
            
        except Exception as e:
            print(f"Error retrieving context: {e}")
            return []
    
    def clear_document_memory(self, document_id: int):
        """Clear all chunks for a specific document"""
        try:
            # Get all chunks for this document
            results = self.collection.get(
                where={"document_id": document_id}
            )
            
            if results["ids"]:
                # Delete all chunks for this document
                self.collection.delete(ids=results["ids"])
                
        except Exception as e:
            print(f"Error clearing document memory: {e}")
    
    def get_memory_stats(self, document_id: int) -> Dict[str, Any]:
        """Get statistics about stored chunks for a document"""
        try:
            results = self.collection.get(
                where={"document_id": document_id}
            )
            
            return {
                "total_chunks": len(results["ids"]) if results["ids"] else 0,
                "document_id": document_id
            }
            
        except Exception as e:
            print(f"Error getting memory stats: {e}")
            return {"total_chunks": 0, "document_id": document_id}
