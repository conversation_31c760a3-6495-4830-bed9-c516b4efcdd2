#!/usr/bin/env python3
"""
Simple backend test
"""

import requests
import time

def test_backend_health():
    """Test if backend is responding"""
    try:
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        print(f"Root endpoint: {response.status_code}")
        return True
    except Exception as e:
        print(f"Backend not responding: {e}")
        return False

def test_docs():
    """Test docs endpoint"""
    try:
        response = requests.get("http://127.0.0.1:8000/docs", timeout=5)
        print(f"Docs endpoint: {response.status_code}")
        return True
    except Exception as e:
        print(f"Docs not available: {e}")
        return False

def test_models_endpoint():
    """Test models endpoint"""
    try:
        # Test the correct endpoint path
        response = requests.get("http://127.0.0.1:8000/api/v1/models", timeout=5)
        print(f"Models list endpoint: {response.status_code}")
        if response.status_code == 200:
            print(f"Models response: {response.json()}")

        # Test connection check
        response2 = requests.get("http://127.0.0.1:8000/api/v1/models/check-connection", timeout=5)
        print(f"Connection check: {response2.status_code}")

        # Test generation
        response3 = requests.post("http://127.0.0.1:8000/api/v1/models/test-generate", timeout=30)
        print(f"Test generation: {response3.status_code}")
        if response3.status_code == 200:
            result = response3.json()
            print(f"Generation result: {result}")

        return True
    except Exception as e:
        print(f"Models endpoint failed: {e}")
        return False

def main():
    print("🔍 Simple Backend Test...")
    print("=" * 30)
    
    # Wait a moment for backend to start
    print("Waiting for backend to start...")
    time.sleep(3)
    
    # Test basic endpoints
    test_backend_health()
    test_docs()
    test_models_endpoint()
    
    print("\nIf all endpoints are working, the issue is specifically with generation.")

if __name__ == "__main__":
    main()
