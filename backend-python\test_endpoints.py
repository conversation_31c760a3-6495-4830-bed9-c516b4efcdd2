#!/usr/bin/env python3
"""
Test script to verify API endpoints are working
"""

import requests
import json

def test_endpoint(url, description):
    """Test a single endpoint"""
    print(f"\n🔍 Testing {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ Success: {json.dumps(data, indent=2)[:200]}...")
            except:
                print(f"✅ Success: {response.text[:200]}...")
        else:
            print(f"❌ Failed: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Server not running")
    except requests.exceptions.Timeout:
        print("❌ Timeout: Server not responding")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Test all endpoints"""
    base_url = "http://127.0.0.1:8001"
    
    endpoints = [
        (f"{base_url}/health", "Health Check"),
        (f"{base_url}/", "Root"),
        (f"{base_url}/api/v1/documents", "Documents List"),
        (f"{base_url}/api/v1/models", "Models List"),
        (f"{base_url}/api/v1/models/status", "Models Status"),
        (f"{base_url}/api/v1/models/check-connection", "Models Connection Check"),
    ]
    
    print("🚀 ASCAES Backend API Endpoint Test")
    print("=" * 50)
    
    for url, description in endpoints:
        test_endpoint(url, description)
    
    print("\n" + "=" * 50)
    print("✅ Test completed!")

if __name__ == "__main__":
    main()
