# ASCAES - Working Procedure & Logic Documentation

## 🎯 Overview

ASCAES (Academic Document Generation Specialist) is an offline-first desktop application that generates academic documents using local AI models through Ollama. This document details the complete working procedure and logic implementation.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │     Ollama      │
│   (React)       │◄──►│   (FastAPI)     │◄──►│  (DeepSeek-R1)  │
│   Port: 5173    │    │   Port: 8000    │    │  Port: 11434    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Electron      │    │   SQLite DB     │    │   ChromaDB      │
│   Desktop App   │    │   Documents     │    │   RAG Memory    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 Complete Generation Flow

### Phase 1: User Interaction & Document Creation

#### 1.1 User Input Processing
```typescript
// Location: frontend-react/src/pages/ChatPage.tsx
const handleSendMessage = async () => {
  const userMessage = inputValue.trim()
  
  // Create document with user's topic
  const documentData = {
    title: userMessage.length > 50 ? userMessage.substring(0, 50) + '...' : userMessage,
    document_type: 'Academic Paper',
    spacing: 'Double',
    pages: 10,
    improve_realism: true,
    enable_grammar_check: true,
    writing_style: 'Analytical',
    cultural_inflection: 'American'
  }
  
  const document = await createDocument(documentData)
  await startGeneration(document.id)
}
```

#### 1.2 Document Creation API
```python
# Location: backend-python/app/api/documents.py
@router.post("/documents", response_model=DocumentResponse)
async def create_document(document: DocumentCreate, db: Session = Depends(get_db)):
    # Calculate estimated words based on pages
    estimated_words = get_word_count_estimate(document.pages, document.spacing)
    
    # Create document record
    db_document = Document(
        title=document.title,
        document_type=document.document_type,
        estimated_words=estimated_words,
        status="pending",
        progress=0.0
    )
    db.add(db_document)
    db.commit()
    return db_document
```

### Phase 2: Generation Initialization

#### 2.1 Start Generation Request
```typescript
// Location: frontend-react/src/store/useAppStore.ts
startGeneration: async (documentId) => {
  const response = await fetch(`${API_BASE_URL}/generate`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ document_id: documentId })
  })
}
```

#### 2.2 Background Task Initialization
```python
# Location: backend-python/app/api/generation.py
@router.post("/generate")
async def start_generation(request: GenerationRequest, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    # Verify document exists
    document = db.query(Document).filter(Document.id == request.document_id).first()
    
    # Start generation as background task
    background_tasks.add_task(run_generation_wrapper, request.document_id, db)
    
    return {"message": "Generation started", "document_id": request.document_id, "status": "generating"}

def run_generation_wrapper(document_id: int, db: Session):
    """Wrapper to run async generation in background task"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(generation_service.run_generation_flow(document_id, db))
    except Exception as e:
        # Update document status to error
        document = db.query(Document).filter(Document.id == document_id).first()
        if document:
            document.status = "error"
            db.commit()
    finally:
        loop.close()
```

### Phase 3: Chapter Outline Generation

#### 3.1 Outline Generation Logic
```python
# Location: backend-python/app/core/generation_service.py
async def generate_chapter_outline(self, document: Document) -> List[str]:
    outline_prompt = f"""
Generate exactly 10 chapter titles for a {document.document_type.lower()} titled "{document.title}".

Requirements:
- Each title should be specific and academic
- Titles should flow logically from introduction to conclusion
- Use {document.writing_style} writing style
- Apply {document.cultural_inflection} cultural perspective

Return only the chapter titles, one per line, without numbers:
"""
    
    response = await self.call_ollama_generate(outline_prompt)
    
    # Parse and clean chapter titles
    lines = [line.strip() for line in response.split('\n') if line.strip()]
    chapter_titles = []
    
    for line in lines:
        # Remove numbering and formatting
        clean_title = line.strip()
        if clean_title.startswith(('1.', '2.', '3.')):
            clean_title = clean_title.split('.', 1)[1].strip()
        
        if clean_title and len(clean_title) > 5:
            chapter_titles.append(clean_title)
    
    # Ensure exactly 10 chapters
    if len(chapter_titles) < 10:
        for i in range(len(chapter_titles), 10):
            chapter_titles.append(f"Chapter {i+1} Content")
    elif len(chapter_titles) > 10:
        chapter_titles = chapter_titles[:10]
    
    return chapter_titles
```

### Phase 4: Sequential Chapter Generation

#### 4.1 Main Generation Flow
```python
# Location: backend-python/app/core/generation_service.py
async def run_generation_flow(self, document_id: int, db: Session):
    document = db.query(Document).filter(Document.id == document_id).first()
    
    try:
        # Update status to generating
        document.status = "generating"
        document.progress = 0.0
        db.commit()
        
        # Clear any existing RAG memory
        self.rag_service.clear_document_memory(document_id)
        
        # Generate chapter outline
        chapter_titles = await self.generate_chapter_outline(document)
        
        # Create chapter records in database
        for i, title in enumerate(chapter_titles):
            chapter = Chapter(
                document_id=document_id,
                chapter_number=i + 1,
                title=title,
                status="pending"
            )
            db.add(chapter)
        db.commit()
        
        # Generate each chapter sequentially
        chapters = db.query(Chapter).filter(Chapter.document_id == document_id).order_by(Chapter.chapter_number).all()
        
        for i, chapter in enumerate(chapters):
            try:
                # Update chapter status
                chapter.status = "generating"
                db.commit()
                
                # Get relevant context from previous chapters (RAG)
                relevant_context = await self.rag_service.retrieve_relevant_context(
                    chapter.title, document_id, n_results=3
                )
                
                # Generate chapter content
                prompt = self.construct_prompt(document, chapter.title, chapter.chapter_number, relevant_context)
                content = await self.call_ollama_generate(prompt)
                
                # Update chapter with generated content
                chapter.content = content
                chapter.word_count = len(content.split())
                chapter.status = "completed"
                db.commit()
                
                # Add chapter content to RAG memory for future chapters
                await self.rag_service.add_text_to_memory(
                    content, document_id, chapter.id, chapter.title
                )
                
                # Update document progress
                document.progress = ((i + 1) / len(chapters)) * 100
                db.commit()
                
                # 6-second delay between chapters to prevent overload
                await asyncio.sleep(6)
                
            except Exception as e:
                print(f"Error generating chapter {chapter.chapter_number}: {e}")
                chapter.status = "error"
                db.commit()
        
        # Mark document as completed
        document.status = "completed"
        document.progress = 100.0
        db.commit()
        
    except Exception as e:
        print(f"Error in generation flow: {e}")
        document.status = "error"
        db.commit()
```

### Phase 5: Prompt Construction & AI Generation

#### 5.1 Dynamic Prompt Building
```python
# Location: backend-python/app/core/generation_service.py
def construct_prompt(self, document: Document, chapter_title: str, chapter_number: int, relevant_context: List[Dict[str, Any]] = None) -> str:
    # Get writing style and cultural inflection definitions
    style_def = WRITING_STYLE_DEFINITIONS.get(document.writing_style, {})
    culture_def = CULTURAL_INFLECTION_DEFINITIONS.get(document.cultural_inflection, {})
    
    # Build context section from previous chapters (RAG)
    context_section = ""
    if relevant_context:
        context_texts = [chunk["text"] for chunk in relevant_context]
        context_section = f"""
### DOCUMENT CONTEXT ###
Here are relevant excerpts from previously written chapters:
{chr(10).join([f"Context {i+1}: {text}" for i, text in enumerate(context_texts)])}
Use this context to maintain consistency and coherence.
"""
    
    # Academic formatting protocols
    academic_formatting = get_academic_formatting_protocol(document.academic_settings)
    
    # Humanization protocol for realism
    humanization = HUMANIZATION_PROTOCOL if document.improve_realism else ""
    
    # Construct complete prompt
    prompt = f"""
### DOCUMENT GENERATION TASK ###
You are writing Chapter {chapter_number} titled "{chapter_title}" for a {document.document_type.lower()} titled "{document.title}".

### WRITING STYLE: {document.writing_style} ###
{style_def.get('DESCRIPTION', '')}

### CULTURAL INFLECTION: {document.cultural_inflection} ###
Rhetorical Style: {culture_def.get('RHETORICAL_STYLE', '')}
Tone: {culture_def.get('TONE', '')}
Key Values: {culture_def.get('KEY_VALUES_EMPHASIZED', '')}

{context_section}

{academic_formatting}

{humanization}

### GENERATION REQUIREMENTS ###
- Write approximately {max(250, document.estimated_words // 10)} words (minimum 250 words)
- Maintain consistency with the document title and type
- Use the specified writing style and cultural inflection
- Create engaging, well-structured content with multiple paragraphs
- Include detailed explanations, examples, and thorough coverage
- Do not include chapter numbers or titles in your response - only the content
- Do not include any reasoning, thinking process, or meta-commentary

### CHAPTER TITLE ###
{chapter_title}

### IMPORTANT ###
- Start writing the chapter content immediately
- Do not include any thinking, reasoning, or planning
- Write only the actual chapter content
- Begin with the first paragraph of the chapter

Generate the chapter content now:
"""

    return prompt

#### 5.2 Ollama API Integration
```python
# Location: backend-python/app/core/generation_service.py
async def call_ollama_generate(self, prompt: str, model: str = None) -> str:
    if model is None:
        model = self.generation_model  # "deepseek-r1:1.5b"

    try:
        # Use requests instead of httpx for better compatibility
        response = requests.post(
            f"{self.ollama_base_url}/api/generate",
            json={
                "model": model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,      # Balanced creativity
                    "top_p": 0.9,           # Nucleus sampling
                    "top_k": 40,            # Top-k sampling
                    "num_predict": 400      # ~250-300 words per chapter
                }
            },
            timeout=90.0  # 90-second timeout for reliability
        )
        response.raise_for_status()
        result = response.json()
        content = result.get("response", "")

        if not content:
            return "Unable to generate content. Please check Ollama model availability."

        # Filter out DeepSeek reasoning/thinking process
        content = self.filter_reasoning_content(content)

        return content

    except Exception as e:
        print(f"Error calling Ollama: {e}")
        return f"Error generating content: {str(e)}"
```

### Phase 6: Content Processing & Filtering

#### 6.1 Reasoning Content Filter
```python
# Location: backend-python/app/core/generation_service.py
def filter_reasoning_content(self, content: str) -> str:
    """Filter out DeepSeek reasoning/thinking process and return only actual content"""
    import re

    # Remove thinking tags and content between them
    content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
    content = re.sub(r'<thinking>.*?</thinking>', '', content, flags=re.DOTALL)

    # Remove lines that look like reasoning
    lines = content.split('\n')
    filtered_lines = []

    skip_patterns = [
        r'^(Alright|Okay|Let me|I need to|I should|I\'ll|First)',
        r'^(The user|The task|The document|The chapter)',
        r'^(Looking at|Considering|Given that|Based on)',
        r'^(So |Now |Next |Then )',
        r'^\d+\.\s*(First|Second|Third|Fourth|Fifth|Sixth|Seventh|Eighth|Ninth|Tenth)',
    ]

    for line in lines:
        line = line.strip()
        if not line:
            filtered_lines.append(line)
            continue

        # Check if line matches reasoning patterns
        is_reasoning = False
        for pattern in skip_patterns:
            if re.match(pattern, line, re.IGNORECASE):
                is_reasoning = True
                break

        # Skip obvious reasoning lines
        if not is_reasoning:
            filtered_lines.append(line)

    # Join back and clean up
    result = '\n'.join(filtered_lines)

    # Remove multiple consecutive newlines
    result = re.sub(r'\n\s*\n\s*\n+', '\n\n', result)

    return result.strip()
```

### Phase 7: RAG (Retrieval-Augmented Generation) System

#### 7.1 Memory Management
```python
# Location: backend-python/app/core/rag_service.py
class RAGService:
    def __init__(self):
        self.ollama_base_url = "http://localhost:11434"
        self.embedding_model = "nomic-embed-text:latest"
        self.collection_name = "ascaes_documents"

    async def add_text_to_memory(self, text: str, document_id: int, chapter_id: int, chapter_title: str):
        """Add chapter content to RAG memory for future context"""
        try:
            # Get embedding for the text
            embedding = await self.get_embedding(text)

            # Store in ChromaDB with metadata
            self.collection.add(
                embeddings=[embedding],
                documents=[text],
                metadatas=[{
                    "document_id": document_id,
                    "chapter_id": chapter_id,
                    "chapter_title": chapter_title,
                    "timestamp": datetime.now().isoformat()
                }],
                ids=[f"doc_{document_id}_ch_{chapter_id}"]
            )
        except Exception as e:
            print(f"Error adding text to memory: {e}")

    async def retrieve_relevant_context(self, query: str, document_id: int, n_results: int = 3) -> List[Dict[str, Any]]:
        """Retrieve relevant context from previous chapters"""
        try:
            query_embedding = await self.get_embedding(query)

            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where={"document_id": document_id}
            )

            context = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    context.append({
                        "text": doc,
                        "metadata": results['metadatas'][0][i],
                        "distance": results['distances'][0][i]
                    })

            return context
        except Exception as e:
            print(f"Error retrieving context: {e}")
            return []
```

### Phase 8: Real-time Progress Updates

#### 8.1 Frontend Polling System
```typescript
// Location: frontend-react/src/pages/ChatPage.tsx
// Poll for generation status when generating
useEffect(() => {
  let interval: NodeJS.Timeout | null = null

  if (isGenerating && currentDocumentId) {
    interval = setInterval(() => {
      fetchGenerationStatus(currentDocumentId)
    }, 2000) // Poll every 2 seconds
  }

  return () => {
    if (interval) {
      clearInterval(interval)
    }
  }
}, [isGenerating, currentDocumentId, fetchGenerationStatus])
```

#### 8.2 Status API Endpoint
```python
# Location: backend-python/app/api/generation.py
@router.get("/generation-status/{document_id}")
async def get_generation_status(document_id: int, db: Session = Depends(get_db)):
    document = db.query(Document).filter(Document.id == document_id).first()
    chapters = db.query(Chapter).filter(Chapter.document_id == document_id).order_by(Chapter.chapter_number).all()

    chapter_statuses = [
        {
            "chapter_number": ch.chapter_number,
            "title": ch.title,
            "status": ch.status,
            "word_count": ch.word_count
        }
        for ch in chapters
    ]

    return {
        "document_id": document_id,
        "status": document.status,
        "progress": document.progress,
        "chapters": chapter_statuses,
        "total_chapters": len(chapters)
    }
```

## 🎯 Key Design Decisions & Logic

### 1. **Sequential Generation**
- Chapters are generated one by one, not in parallel
- Each chapter uses context from previous chapters via RAG
- 6-second delay between chapters prevents Ollama overload

### 2. **Token Optimization**
- Limited to 400 tokens per chapter (~250-300 words)
- Balances quality with reliability
- Prevents timeout issues with DeepSeek model

### 3. **Error Handling**
- Graceful degradation: if one chapter fails, others continue
- Background task isolation prevents frontend blocking
- Comprehensive logging for debugging

### 4. **Memory Management**
- RAG system maintains document coherence
- ChromaDB stores embeddings of previous chapters
- Context retrieval ensures consistency across chapters

### 5. **Real-time Updates**
- Frontend polls backend every 2 seconds
- Progress persists across navigation
- Global state management with Zustand

## 🔧 Technical Specifications

- **AI Model**: DeepSeek-R1:1.5b (1.1GB, reasoning model)
- **Embedding Model**: nomic-embed-text:latest (274MB)
- **Database**: SQLite for documents/chapters, ChromaDB for RAG
- **API Timeout**: 90 seconds per chapter
- **Generation Delay**: 6 seconds between chapters
- **Target Length**: 250+ words per chapter
- **Total Chapters**: Always 10 chapters per document

## 🎉 Result

This architecture ensures reliable, high-quality academic document generation with:
- ✅ Consistent content across chapters
- ✅ Real-time progress tracking
- ✅ Offline-first operation
- ✅ Error resilience
- ✅ Scalable design
```
