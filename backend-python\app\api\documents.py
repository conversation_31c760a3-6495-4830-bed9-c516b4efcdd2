"""
Documents API Routes
CRUD operations for documents and chapters
"""

from fastapi import API<PERSON>outer, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.db.database import get_db
from app.db.models import (
    Document, Chapter, DocumentCreate, DocumentResponse, ChapterResponse
)
from app.core.protocols import get_word_count_estimate

router = APIRouter()

@router.post("/documents", response_model=DocumentResponse)
async def create_document(document_data: DocumentCreate, db: Session = Depends(get_db)):
    """Create a new document"""
    
    # Calculate estimated word count
    estimated_words = get_word_count_estimate(
        document_data.pages, 
        document_data.document_type, 
        document_data.spacing
    )
    
    # Create document record
    document = Document(
        title=document_data.title,
        document_type=document_data.document_type,
        spacing=document_data.spacing,
        pages=document_data.pages,
        estimated_words=estimated_words,
        improve_realism=document_data.improve_realism,
        enable_grammar_check=document_data.enable_grammar_check,
        writing_style=document_data.writing_style,
        cultural_inflection=document_data.cultural_inflection,
        enable_academic_formatting=document_data.academic_formatting.enable_academic_formatting,
        citation_style=document_data.academic_formatting.citation_style,
        student_name=document_data.academic_formatting.student_name,
        course_name=document_data.academic_formatting.course_name,
        instructor_name=document_data.academic_formatting.instructor_name,
        due_date=document_data.academic_formatting.due_date,
        status="pending"
    )
    
    db.add(document)
    db.commit()
    db.refresh(document)
    
    return document

@router.get("/documents", response_model=List[DocumentResponse])
async def get_documents(db: Session = Depends(get_db)):
    """Get all documents"""
    documents = db.query(Document).order_by(Document.created_at.desc()).all()
    return documents

@router.get("/documents/{document_id}", response_model=DocumentResponse)
async def get_document(document_id: int, db: Session = Depends(get_db)):
    """Get a specific document"""
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    return document

@router.delete("/documents/{document_id}")
async def delete_document(document_id: int, db: Session = Depends(get_db)):
    """Delete a document and all its chapters"""
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Clear RAG memory for this document
    from app.core.rag_service import RAGService
    rag_service = RAGService()
    rag_service.clear_document_memory(document_id)
    
    db.delete(document)
    db.commit()
    
    return {"message": "Document deleted successfully"}

@router.get("/documents/{document_id}/chapters", response_model=List[ChapterResponse])
async def get_document_chapters(document_id: int, db: Session = Depends(get_db)):
    """Get all chapters for a document"""
    # Verify document exists
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    chapters = db.query(Chapter).filter(Chapter.document_id == document_id).order_by(Chapter.chapter_number).all()
    return chapters

@router.get("/documents/{document_id}/chapters/{chapter_id}", response_model=ChapterResponse)
async def get_chapter(document_id: int, chapter_id: int, db: Session = Depends(get_db)):
    """Get a specific chapter"""
    chapter = db.query(Chapter).filter(
        Chapter.id == chapter_id,
        Chapter.document_id == document_id
    ).first()
    
    if not chapter:
        raise HTTPException(status_code=404, detail="Chapter not found")
    
    return chapter

@router.get("/word-count-estimate")
async def get_word_count_estimate_endpoint(pages: int, doc_type: str, spacing: str):
    """Get estimated word count for document parameters"""
    try:
        estimated_words = get_word_count_estimate(pages, doc_type, spacing)
        return {"estimated_words": estimated_words}
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error calculating word count: {str(e)}")

@router.get("/documents/{document_id}/export")
async def export_document(document_id: int, db: Session = Depends(get_db)):
    """Export complete document as text"""
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    chapters = db.query(Chapter).filter(Chapter.document_id == document_id).order_by(Chapter.chapter_number).all()
    
    # Build complete document text
    document_text = f"{document.title}\n\n"
    
    for chapter in chapters:
        if chapter.content:
            document_text += f"Chapter {chapter.chapter_number}: {chapter.title}\n\n"
            document_text += f"{chapter.content}\n\n"
    
    return {
        "document_id": document_id,
        "title": document.title,
        "full_text": document_text,
        "total_words": len(document_text.split()),
        "total_chapters": len(chapters)
    }
