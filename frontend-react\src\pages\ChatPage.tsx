import React, { useState, useRef, useEffect } from 'react'
import { Send, Settings, Play, Square, FileText, Download, Eye, EyeOff, ChevronDown, Bug } from 'lucide-react'
import { useAppStore } from '../store/useAppStore'
import ChatSettingsPanel from '../components/ChatSettingsPanel'
import DebugPanel from '../components/DebugPanel'

interface Message {
  id: string
  type: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
}

const ChatPage: React.FC = () => {
  // Load messages from localStorage or use default
  const [messages, setMessages] = useState<Message[]>(() => {
    const savedMessages = localStorage.getItem('ascaes-chat-messages')
    if (savedMessages) {
      try {
        const parsed = JSON.parse(savedMessages)
        return parsed.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))
      } catch (e) {
        console.error('Failed to parse saved messages:', e)
      }
    }
    return [
      {
        id: '1',
        type: 'system',
        content: 'Welcome to ASCAES! Enter your topic or document requirements to start generating academic content.',
        timestamp: new Date()
      }
    ]
  })
  const [inputValue, setInputValue] = useState('')
  const [showSettings, setShowSettings] = useState(false)
  const [showPreview, setShowPreview] = useState(true)

  const [savedSettings, setSavedSettings] = useState<any>(null)
  const [showDebugPanel, setShowDebugPanel] = useState(false)

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const {
    createDocument,
    startGeneration,
    fetchGenerationStatus,
    generationStatus,
    isLoading,
    currentDocument
  } = useAppStore()

  // Use store state for generation tracking
  const isGenerating = generationStatus?.status === 'generating' || generationStatus?.status === 'in_progress'
  const currentDocumentId = currentDocument?.id || generationStatus?.document_id
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }
  
  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Poll for generation status when generating
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null

    if (isGenerating && currentDocumentId) {
      // Start polling immediately
      fetchGenerationStatus(currentDocumentId)

      interval = setInterval(() => {
        fetchGenerationStatus(currentDocumentId)
      }, 3000) // Poll every 3 seconds
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [isGenerating, currentDocumentId, fetchGenerationStatus])

  // Update generation status and add progress messages
  useEffect(() => {
    if (generationStatus) {
      if (generationStatus.status === 'completed') {
        addMessage('system', 'Document generation completed! You can view it in the Documents section.')
      } else if (generationStatus.status === 'generating') {
        const completedChapters = generationStatus.chapters.filter(ch => ch.status === 'completed').length
        const totalChapters = generationStatus.total_chapters
        if (completedChapters > 0) {
          addMessage('system', `Progress: ${completedChapters}/${totalChapters} chapters completed (${Math.round(generationStatus.progress)}%)`)
        }
      }
    }
  }, [generationStatus])
  
  const addMessage = (type: 'user' | 'assistant' | 'system', content: string) => {
    const newMessage: Message = {
      id: Date.now().toString(),
      type,
      content,
      timestamp: new Date()
    }
    setMessages(prev => {
      const updated = [...prev, newMessage]
      // Save to localStorage
      localStorage.setItem('ascaes-chat-messages', JSON.stringify(updated))
      return updated
    })
  }
  
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isGenerating) return

    const userMessage = inputValue.trim()
    console.log('ChatPage: Sending message:', userMessage)
    setInputValue('')
    addMessage('user', userMessage)

    // Show thinking message
    addMessage('system', 'Processing your request...')

    // Create document with the user's topic using saved settings or defaults
    const documentData = savedSettings ? {
      ...savedSettings,
      title: userMessage.length > 50 ? userMessage.substring(0, 50) + '...' : userMessage,
    } : {
      title: userMessage.length > 50 ? userMessage.substring(0, 50) + '...' : userMessage,
      document_type: 'Academic Paper',
      spacing: 'Double',
      pages: 10,
      improve_realism: true,
      enable_grammar_check: true,
      writing_style: 'Analytical',
      cultural_inflection: { 'American': 100 },
      academic_formatting: {
        enable_academic_formatting: false,
        citation_style: 'APA',
        student_name: '',
        course_name: '',
        instructor_name: '',
        due_date: ''
      }
    }

    console.log('ChatPage: Document data prepared:', documentData)

    try {
      console.log('ChatPage: Creating document...')
      const document = await createDocument(documentData)
      console.log('ChatPage: Document created:', document)

      addMessage('system', `Document "${document.title}" created successfully. Starting generation...`)

      // Start generation
      console.log('ChatPage: Starting generation...')
      await startGeneration(document.id)
      console.log('ChatPage: Generation started successfully')

      addMessage('system', 'Generation started! This may take several minutes. You can monitor progress below.')

    } catch (error) {
      console.error('ChatPage: Error in handleSendMessage:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      addMessage('system', `Error: ${errorMessage}. Please check the console for more details.`)
    }
  }
  
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }
  
  const handleStopGeneration = () => {
    // TODO: Implement actual stop generation API call
    addMessage('system', 'Generation stopped.')
  }
  
  const handleSaveSettings = (formData: any) => {
    setSavedSettings(formData)
    setShowSettings(false)
    addMessage('system', 'Settings saved! Your next document will use these settings.')
  }
  
  return (
    <div className="flex h-full relative">
      {/* Top Right Controls */}
      <div className="absolute top-2 right-2 flex space-x-1 z-20">
        <button
          onClick={() => setShowPreview(!showPreview)}
          className="btn-secondary p-2 rounded-lg"
          title={showPreview ? 'Hide Preview' : 'Show Preview'}
        >
          {showPreview ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
        </button>

        <button
          onClick={() => setShowSettings(!showSettings)}
          className="btn-secondary p-2 rounded-lg"
          title="Settings"
        >
          <Settings className="w-4 h-4" />
        </button>

        <button
          onClick={() => setShowDebugPanel(true)}
          className="btn-secondary p-2 rounded-lg"
          title="Debug Panel"
        >
          <Bug className="w-4 h-4" />
        </button>


      </div>

      {/* Chat Section */}
      <div className={`${showPreview ? 'w-1/2' : 'flex-1'} flex flex-col border-r border-dark-border`}>
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-3xl p-4 rounded-lg ${
                  message.type === 'user'
                    ? 'bg-dark-accent text-white'
                    : message.type === 'system'
                    ? 'bg-dark-warning/10 text-dark-warning border border-dark-warning/20'
                    : 'bg-dark-panel text-dark-text border border-dark-border'
                }`}
              >
                <p className="whitespace-pre-wrap">{message.content}</p>
                <p className="text-xs opacity-70 mt-2">
                  {message.timestamp.toLocaleTimeString()}
                </p>
              </div>
            </div>
          ))}
          
          {/* Generation Progress */}
          {isGenerating && generationStatus && (
            <div className="bg-dark-panel border border-dark-border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-dark-text">Generation Progress</span>
                <span className="text-sm text-dark-text-secondary">
                  {Math.round(generationStatus.progress)}%
                </span>
              </div>
              <div className="w-full bg-dark-border rounded-full h-3 mb-2">
                <div 
                  className="bg-dark-accent h-3 rounded-full transition-all duration-500"
                  style={{ width: `${generationStatus.progress}%` }}
                />
              </div>
              <p className="text-sm text-dark-text-secondary">
                {generationStatus.chapters.filter(ch => ch.status === 'completed').length} of {generationStatus.total_chapters} chapters completed
              </p>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
        
        {/* Input Area */}
        <div className="border-t border-dark-border p-4">
          <div className="flex items-end space-x-3">
            <div className="flex-1">
              <textarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Enter your topic or document requirements..."
                className="input-field w-full resize-none"
                rows={3}
                disabled={isGenerating}
              />
            </div>
            
            {isGenerating ? (
              <button
                onClick={handleStopGeneration}
                className="btn-danger p-3 rounded-lg"
                title="Stop Generation"
              >
                <Square className="w-5 h-5" />
              </button>
            ) : (
              <button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isLoading}
                className="btn-primary p-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                title="Send Message"
              >
                <Send className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* PDF Preview Section */}
      {showPreview && (
        <div className="w-1/2 flex flex-col bg-dark-panel">
          {/* Preview Header */}
          <div className="border-b border-dark-border p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <FileText className="w-5 h-5 text-dark-accent" />
                <div>
                  <h3 className="font-semibold text-dark-text">Document Preview</h3>
                  <p className="text-sm text-dark-text-secondary">
                    {currentDocument ? currentDocument.title : 'No document selected'}
                  </p>
                </div>
              </div>

              {currentDocument && (
                <button
                  className="btn-primary flex items-center space-x-2"
                  onClick={() => {
                    // TODO: Implement export functionality
                    alert('Export functionality coming soon!')
                  }}
                >
                  <Download className="w-4 h-4" />
                  <span>Export</span>
                </button>
              )}
            </div>
          </div>

          {/* Preview Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {currentDocument ? (
              <div className="space-y-6">
                {/* Document Title */}
                <div className="text-center border-b border-dark-border pb-4">
                  <h1 className="text-2xl font-bold text-dark-text mb-2">
                    {currentDocument.title}
                  </h1>
                  <p className="text-dark-text-secondary">
                    {currentDocument.document_type} • {currentDocument.pages} pages
                  </p>
                </div>

                {/* PDF-like Document View */}
                {generationStatus?.chapters && generationStatus.chapters.length > 0 ? (
                  <div className="bg-white text-black p-8 rounded-lg shadow-lg max-w-4xl mx-auto" style={{ fontFamily: 'Times New Roman, serif' }}>
                    {/* Document Header */}
                    <div className="text-center mb-8 border-b-2 border-gray-300 pb-6">
                      <h1 className="text-3xl font-bold mb-4">{currentDocument?.title}</h1>
                      <div className="text-sm text-gray-600">
                        <p>{currentDocument?.document_type}</p>
                        <p>Generated by ASCAES Academic Document Specialist</p>
                        <p>{new Date().toLocaleDateString()}</p>
                      </div>
                    </div>

                    {/* Table of Contents */}
                    <div className="mb-8">
                      <h2 className="text-xl font-bold mb-4">Table of Contents</h2>
                      <div className="space-y-1">
                        {generationStatus.chapters.map((chapter, index) => (
                          <div key={index} className="flex justify-between items-center py-1">
                            <span className="text-sm">
                              Chapter {chapter.chapter_number}: {chapter.title}
                            </span>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm">{index + 1}</span>
                              <span className={`w-2 h-2 rounded-full ${
                                chapter.status === 'completed' ? 'bg-green-500' :
                                chapter.status === 'generating' ? 'bg-yellow-500' : 'bg-gray-400'
                              }`}></span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Chapters Content */}
                    <div className="space-y-8">
                      {generationStatus.chapters.map((chapter, index) => (
                        <div key={index} className="chapter-content">
                          {/* Chapter Header */}
                          <div className="mb-6">
                            <h2 className="text-2xl font-bold mb-2">
                              Chapter {chapter.chapter_number}
                            </h2>
                            <h3 className="text-xl font-semibold text-gray-700 mb-4">
                              {chapter.title}
                            </h3>
                          </div>

                          {/* Chapter Content */}
                          {chapter.status === 'completed' && chapter.content && (
                            <div className="prose prose-lg max-w-none">
                              <div className="text-justify leading-relaxed text-gray-900 whitespace-pre-wrap">
                                {chapter.content}
                              </div>
                              <div className="mt-4 text-sm text-gray-500">
                                Word count: {chapter.word_count || chapter.content.split(' ').length} words
                              </div>
                            </div>
                          )}

                          {chapter.status === 'generating' && (
                            <div className="flex items-center justify-center py-8 text-gray-600">
                              <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-3"></div>
                              <span>Generating chapter content...</span>
                            </div>
                          )}

                          {chapter.status === 'pending' && (
                            <div className="text-center py-8 text-gray-500 italic">
                              <div className="w-full h-32 bg-gray-100 rounded-lg flex items-center justify-center">
                                Waiting to generate content...
                              </div>
                            </div>
                          )}

                          {/* Page break simulation */}
                          {index < generationStatus.chapters.length - 1 && (
                            <div className="border-t border-gray-300 mt-8 pt-8"></div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FileText className="w-12 h-12 text-dark-text-secondary mx-auto mb-4" />
                    <p className="text-dark-text-secondary">
                      Start a conversation to generate document content
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <FileText className="w-12 h-12 text-dark-text-secondary mx-auto mb-4" />
                <p className="text-dark-text-secondary">
                  No document selected. Start a conversation to create a document.
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Settings Sidebar */}
      {showSettings && (
        <div className="w-96 border-l border-dark-border bg-dark-bg p-6 overflow-y-auto absolute right-0 top-0 bottom-0 z-10 shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-dark-text">Document Settings</h3>
            <button
              onClick={() => setShowSettings(false)}
              className="text-dark-text-secondary hover:text-dark-text"
            >
              ×
            </button>
          </div>
          
          <ChatSettingsPanel
            onSubmit={handleSaveSettings}
            isLoading={isLoading}
          />
        </div>
      )}

      {/* Debug Panel */}
      <DebugPanel
        isOpen={showDebugPanel}
        onClose={() => setShowDebugPanel(false)}
      />
    </div>
  )
}

export default ChatPage
