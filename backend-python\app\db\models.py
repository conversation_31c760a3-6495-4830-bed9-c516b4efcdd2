"""
SQLAlchemy database models
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Float, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base
from pydantic import BaseModel
from typing import Optional, List, Dict, Union
from datetime import datetime

class Document(Base):
    __tablename__ = "documents"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, nullable=False)
    document_type = Column(String, nullable=False)  # Academic Paper, Book, Report
    spacing = Column(String, nullable=False)  # Double, Single
    pages = Column(Integer, nullable=False)
    estimated_words = Column(Integer, nullable=False)
    
    # Settings
    improve_realism = Column(Boolean, default=False)
    enable_grammar_check = Column(Boolean, default=False)
    writing_style = Column(String, nullable=False)
    cultural_inflection = Column(JSON, nullable=False)
    
    # Academic formatting
    enable_academic_formatting = Column(Boolean, default=False)
    citation_style = Column(String, nullable=True)
    student_name = Column(String, nullable=True)
    course_name = Column(String, nullable=True)
    instructor_name = Column(String, nullable=True)
    due_date = Column(String, nullable=True)
    
    # Status
    status = Column(String, default="pending")  # pending, generating, completed, error
    progress = Column(Float, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    chapters = relationship("Chapter", back_populates="document", cascade="all, delete-orphan")

class Chapter(Base):
    __tablename__ = "chapters"
    
    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    chapter_number = Column(Integer, nullable=False)
    title = Column(String, nullable=False)
    content = Column(Text, nullable=True)
    word_count = Column(Integer, default=0)
    target_words = Column(Integer, default=400)  # Target word count for this chapter
    status = Column(String, default="pending")  # pending, generating, completed
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    document = relationship("Document", back_populates="chapters")

# Pydantic models for API
class AcademicFormattingSettings(BaseModel):
    enable_academic_formatting: bool = False
    citation_style: Optional[str] = None
    student_name: Optional[str] = None
    course_name: Optional[str] = None
    instructor_name: Optional[str] = None
    due_date: Optional[str] = None

class DocumentCreate(BaseModel):
    title: str
    document_type: str
    spacing: str
    pages: int
    improve_realism: bool = False
    enable_grammar_check: bool = False
    writing_style: str
    cultural_inflection: Union[str, Dict[str, int]]
    academic_formatting: AcademicFormattingSettings

class DocumentResponse(BaseModel):
    id: int
    title: str
    document_type: str
    spacing: str
    pages: int
    estimated_words: int
    status: str
    progress: float
    created_at: datetime
    
    class Config:
        from_attributes = True

class ChapterResponse(BaseModel):
    id: int
    chapter_number: int
    title: str
    content: Optional[str]
    word_count: int
    status: str
    
    class Config:
        from_attributes = True

class GenerationRequest(BaseModel):
    document_id: int

class GrammarCheckRequest(BaseModel):
    text: str

class GrammarCheckResponse(BaseModel):
    corrected_text: str
