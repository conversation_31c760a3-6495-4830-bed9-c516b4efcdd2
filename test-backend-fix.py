#!/usr/bin/env python3
"""
Test the backend API with the fixed Ollama endpoint
"""

import requests
import json

def test_backend_generation():
    """Test the backend generation API"""
    print("Testing backend generation API...")
    
    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/models/test-generate",
            timeout=60
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success! Response: {json.dumps(result, indent=2)}")
            
            if result.get("status") == "success":
                content = result.get("response", "")
                print(f"\nGenerated content: {content}")
                print(f"Word count: {len(content.split())} words")
                return True
            else:
                print(f"Generation failed: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"HTTP Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"Exception: {e}")
        return False

def test_backend_connection():
    """Test backend connection"""
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/models/check-connection", timeout=10)
        print(f"Backend connection: {response.status_code}")
        if response.status_code == 200:
            print(f"Connection response: {response.json()}")
            return True
        return False
    except Exception as e:
        print(f"Backend connection failed: {e}")
        return False

def main():
    print("🔍 Testing Backend with Fixed Ollama API...")
    print("=" * 50)
    
    # Test connection first
    if not test_backend_connection():
        print("❌ Backend not available")
        return
    
    print()
    
    # Test generation
    success = test_backend_generation()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Backend generation is working!")
        print("✅ Document generation should now work in ASCAES")
    else:
        print("❌ Backend generation still failing")

if __name__ == "__main__":
    main()
