#!/usr/bin/env python3
"""
Simple test script to check if backend can start
"""

import sys
import os

# Add the app directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Testing backend imports...")
    
    # Test basic imports
    from app.main import app
    print("✅ FastAPI app imported successfully")
    
    # Test database
    from app.db.database import engine, Base
    print("✅ Database engine created successfully")
    
    # Test models
    from app.db.models import Document, Chapter
    print("✅ Models imported successfully")
    
    # Create tables
    Base.metadata.create_all(bind=engine)
    print("✅ Database tables created successfully")
    
    # Test generation service
    from app.core.generation_service import GenerationService
    service = GenerationService()
    print("✅ Generation service initialized successfully")
    
    print("\n🎉 Backend is ready to start!")
    print("Run: python -m uvicorn app.main:app --host 127.0.0.1 --port 8000")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
