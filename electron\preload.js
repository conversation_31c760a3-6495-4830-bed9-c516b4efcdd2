const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Menu events
  onMenuNewDocument: (callback) => {
    ipcRenderer.on('menu-new-document', callback)
  },
  
  // Remove listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel)
  },
  
  // App info
  getAppVersion: () => {
    return process.env.npm_package_version || '1.0.0'
  },
  
  // Platform info
  getPlatform: () => {
    return process.platform
  }
})

// Security: Remove Node.js globals from renderer process
delete window.require
delete window.exports
delete window.module
