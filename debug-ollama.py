#!/usr/bin/env python3
"""
Debug script to test Ollama API directly
"""

import requests
import json
import time

def test_ollama_chat():
    """Test the chat API directly"""
    print("Testing Ollama chat API...")
    
    url = "http://localhost:11434/api/chat"
    payload = {
        "model": "deepseek-r1:1.5b",
        "messages": [
            {
                "role": "user",
                "content": "Write a short paragraph about artificial intelligence."
            }
        ],
        "stream": False
    }
    
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print("\nSending request...")
    
    try:
        response = requests.post(url, json=payload, timeout=60)
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            
            content = result.get("message", {}).get("content", "")
            print(f"\nExtracted content: {content}")
            print(f"Content length: {len(content)} characters")
            print(f"Word count: {len(content.split())} words")
            
            return True
        else:
            print(f"Error response: {response.text}")
            return False
            
    except Exception as e:
        print(f"Exception occurred: {e}")
        return False

def test_ollama_generate():
    """Test the old generate API to see if it works"""
    print("\nTesting old generate API...")
    
    url = "http://localhost:11434/api/generate"
    payload = {
        "model": "deepseek-r1:1.5b",
        "prompt": "Write a short paragraph about artificial intelligence.",
        "stream": False
    }
    
    try:
        response = requests.post(url, json=payload, timeout=60)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"Error response: {response.text}")
            return False
            
    except Exception as e:
        print(f"Exception occurred: {e}")
        return False

def test_backend_api():
    """Test the backend API"""
    print("\nTesting backend API...")
    
    try:
        # Test connection
        response = requests.get("http://127.0.0.1:8000/api/v1/models/check-connection", timeout=10)
        print(f"Backend connection status: {response.status_code}")
        if response.status_code == 200:
            print(f"Backend response: {response.json()}")
        
        # Test generation
        response = requests.post("http://127.0.0.1:8000/api/v1/models/test-generate", timeout=60)
        print(f"Backend generation status: {response.status_code}")
        if response.status_code == 200:
            print(f"Backend generation response: {response.json()}")
        else:
            print(f"Backend generation error: {response.text}")
            
    except Exception as e:
        print(f"Backend test failed: {e}")

def main():
    print("🔍 Debugging Ollama API Issues...")
    print("=" * 50)
    
    # Test 1: Direct Ollama chat API
    success1 = test_ollama_chat()
    
    # Test 2: Old generate API
    success2 = test_ollama_generate()
    
    # Test 3: Backend API
    test_backend_api()
    
    print("\n" + "=" * 50)
    print("Summary:")
    print(f"Chat API: {'✅ Working' if success1 else '❌ Failed'}")
    print(f"Generate API: {'✅ Working' if success2 else '❌ Failed'}")

if __name__ == "__main__":
    main()
