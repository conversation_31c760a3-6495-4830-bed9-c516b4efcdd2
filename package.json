{"name": "ascaes-app", "version": "1.0.0", "description": "Academic Document Generation Specialist - Offline AI-Powered Writing Tool", "main": "electron/main.js", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\" \"npm run dev:electron\"", "dev:frontend": "cd frontend-react && npm run dev", "dev:backend": "cd backend-python && python -m uvicorn app.main:app --reload --host 127.0.0.1 --port 8000", "dev:electron": "wait-on http://localhost:5173 http://localhost:8000 && electron .", "build": "npm run build:frontend && npm run build:electron", "build:frontend": "cd frontend-react && npm run build", "build:electron": "electron-builder", "install:all": "npm install && cd frontend-react && npm install && cd ../backend-python && pip install -r requirements.txt"}, "dependencies": {"concurrently": "^8.2.2", "wait-on": "^7.2.0"}, "devDependencies": {"electron": "^28.1.0", "electron-builder": "^24.9.1"}, "build": {"appId": "com.ascaes.app", "productName": "ASCAES", "directories": {"output": "dist"}, "files": ["electron/**/*", "frontend-react/dist/**/*"], "extraResources": [{"from": "backend-python", "to": "backend-python", "filter": ["**/*", "!**/__pycache__/**/*", "!**/.*"]}]}}