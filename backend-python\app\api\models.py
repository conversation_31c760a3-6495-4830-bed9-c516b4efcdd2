"""
Models API Routes
Manage and list available Ollama models
"""

from fastapi import APIRouter, HTTPException
import httpx
from typing import List, Dict, Any

router = APIRouter()

@router.get("/models")
async def list_ollama_models():
    """List all available Ollama models"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:11434/api/tags", timeout=10.0)
            response.raise_for_status()
            data = response.json()
            
            models = []
            if "models" in data:
                for model in data["models"]:
                    models.append({
                        "name": model.get("name", ""),
                        "size": model.get("size", 0),
                        "modified_at": model.get("modified_at", ""),
                        "digest": model.get("digest", "")
                    })
            
            return {"models": models, "total": len(models)}
            
    except httpx.RequestError:
        raise HTTPException(
            status_code=503, 
            detail="Ollama service is not available. Please ensure Ollama is running on localhost:11434"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching models: {str(e)}")

@router.get("/models/check-connection")
async def check_ollama_connection():
    """Check if Ollama service is available"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:11434/api/version", timeout=5.0)
            response.raise_for_status()
            data = response.json()
            
            return {
                "status": "connected",
                "version": data.get("version", "unknown"),
                "message": "Ollama service is available"
            }
            
    except httpx.RequestError:
        return {
            "status": "disconnected",
            "message": "Ollama service is not available. Please ensure Ollama is running on localhost:11434"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Error connecting to Ollama: {str(e)}"
        }

@router.post("/models/pull")
async def pull_model(model_name: str):
    """Pull/download a model from Ollama"""
    try:
        async with httpx.AsyncClient(timeout=300.0) as client:  # 5 minute timeout for model pulling
            response = await client.post(
                "http://localhost:11434/api/pull",
                json={"name": model_name}
            )
            response.raise_for_status()
            
            return {
                "status": "success",
                "message": f"Model {model_name} pulled successfully"
            }
            
    except httpx.RequestError:
        raise HTTPException(
            status_code=503,
            detail="Ollama service is not available"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error pulling model: {str(e)}")

@router.get("/models/recommended")
async def get_recommended_models():
    """Get list of recommended models for ASCAES"""
    return {
        "generation_models": [
            {
                "name": "deepseek-r1:latest",
                "description": "Advanced reasoning model, excellent for academic writing",
                "size": "Large (~40GB)",
                "recommended": True
            },
            {
                "name": "llama3:70b",
                "description": "High-quality instruction-tuned model",
                "size": "Very Large (~70GB)",
                "recommended": True
            },
            {
                "name": "llama3:8b",
                "description": "Smaller but capable model for faster generation",
                "size": "Medium (~8GB)",
                "recommended": False
            }
        ],
        "embedding_models": [
            {
                "name": "nomic-embed-text",
                "description": "High-quality text embedding model for RAG",
                "size": "Small (~1GB)",
                "recommended": True
            },
            {
                "name": "all-minilm",
                "description": "Alternative embedding model",
                "size": "Small (~500MB)",
                "recommended": False
            }
        ]
    }
