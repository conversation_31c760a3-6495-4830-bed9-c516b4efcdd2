"""
Models API Routes
Manage and list available Ollama models
"""

from fastapi import APIRouter, HTTPException
import httpx
import requests
from typing import List, Dict, Any

router = APIRouter()

@router.get("/")
async def list_ollama_models():
    """List all available Ollama models"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:11434/api/tags", timeout=10.0)
            response.raise_for_status()
            data = response.json()
            
            models = []
            if "models" in data:
                for model in data["models"]:
                    models.append({
                        "name": model.get("name", ""),
                        "size": model.get("size", 0),
                        "modified_at": model.get("modified_at", ""),
                        "digest": model.get("digest", "")
                    })
            
            return {"models": models, "total": len(models)}
            
    except httpx.RequestError:
        raise HTTPException(
            status_code=503, 
            detail="Ollama service is not available. Please ensure Ollama is running on localhost:11434"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching models: {str(e)}")

@router.get("/status")
def get_models_status():
    """Get Ollama connection status - used by frontend debug panel"""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/version", timeout=5.0)
        response.raise_for_status()
        data = response.json()

        return {
            "connected": True,
            "status": "connected",
            "version": data.get("version", "unknown"),
            "ollama_url": "http://localhost:11434",
            "message": "Ollama service is available"
        }

    except requests.RequestException:
        return {
            "connected": False,
            "status": "disconnected",
            "ollama_url": "http://localhost:11434",
            "error": "Ollama service is not available. Please ensure Ollama is running on localhost:11434"
        }
    except Exception as e:
        return {
            "connected": False,
            "status": "error",
            "ollama_url": "http://localhost:11434",
            "error": f"Error connecting to Ollama: {str(e)}"
        }

@router.get("/check-connection")
async def check_ollama_connection():
    """Check if Ollama service is available"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:11434/api/version", timeout=5.0)
            response.raise_for_status()
            data = response.json()

            return {
                "status": "connected",
                "version": data.get("version", "unknown"),
                "message": "Ollama service is available"
            }

    except httpx.RequestError:
        return {
            "status": "disconnected",
            "message": "Ollama service is not available. Please ensure Ollama is running on localhost:11434"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Error connecting to Ollama: {str(e)}"
        }

@router.post("/test-generate")
async def test_ollama_generate():
    """Test text generation with Ollama"""
    try:
        print("=== STARTING TEST GENERATION ===")
        print("Making request to Ollama...")
        # Use requests instead of httpx to avoid timeout issues
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "deepseek-r1:1.5b",
                "prompt": "Write a short paragraph about artificial intelligence. Write ONLY the content, no thinking or reasoning.",
                "stream": False,
                "options": {
                    "num_predict": 300,  # Increased response length
                    "temperature": 0.7
                    # Removed stop sequences - they were causing empty responses
                }
            },
            timeout=120.0  # Longer timeout for proper content
        )
        print(f"Response status: {response.status_code}")
        response.raise_for_status()
        result = response.json()
        print(f"Response data: {result}")

        # Apply reasoning filter to the response
        raw_content = result.get("response", "")
        print(f"Raw content length: {len(raw_content)}")
        print(f"Raw content preview: {raw_content[:200]}...")

        from app.core.generation_service import GenerationService
        gen_service = GenerationService()
        filtered_content = gen_service.filter_reasoning_content(raw_content)
        print(f"Filtered content length: {len(filtered_content)}")
        print(f"Filtered content preview: {filtered_content[:200]}...")

        return {
            "status": "success",
            "response": filtered_content,
            "model": result.get("model", "")
        }
    except Exception as e:
        print(f"Exception in test_ollama_generate: {e}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": str(e)}

def filter_reasoning_content(content: str) -> str:
    """Filter out DeepSeek reasoning/thinking process and return only the actual content"""
    import re

    # Remove thinking tags and content between them
    content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
    content = re.sub(r'<thinking>.*?</thinking>', '', content, flags=re.DOTALL)

    # Remove lines that look like reasoning
    lines = content.split('\n')
    filtered_lines = []

    skip_patterns = [
        r'^(Alright|Okay|Let me|I need to|I should|I\'ll|First)',
        r'^(The user|The task|The document|The chapter)',
        r'^(Looking at|Considering|Given that|Based on)',
        r'^(So |Now |Next |Then )',
        r'^\d+\.\s*(First|Second|Third|Fourth|Fifth|Sixth|Seventh|Eighth|Ninth|Tenth)',
    ]

    for line in lines:
        line = line.strip()
        if not line:
            filtered_lines.append(line)
            continue

        # Check if line matches reasoning patterns
        is_reasoning = False
        for pattern in skip_patterns:
            if re.match(pattern, line, re.IGNORECASE):
                is_reasoning = True
                break

        # Skip obvious reasoning lines
        if not is_reasoning:
            filtered_lines.append(line)

    # Join back and clean up
    result = '\n'.join(filtered_lines)

    # Remove multiple consecutive newlines
    result = re.sub(r'\n\s*\n\s*\n+', '\n\n', result)

    return result.strip()

@router.post("/pull")
async def pull_model(model_name: str):
    """Pull/download a model from Ollama"""
    try:
        async with httpx.AsyncClient(timeout=300.0) as client:  # 5 minute timeout for model pulling
            response = await client.post(
                "http://localhost:11434/api/pull",
                json={"name": model_name}
            )
            response.raise_for_status()
            
            return {
                "status": "success",
                "message": f"Model {model_name} pulled successfully"
            }
            
    except httpx.RequestError:
        raise HTTPException(
            status_code=503,
            detail="Ollama service is not available"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error pulling model: {str(e)}")

@router.get("/recommended")
async def get_recommended_models():
    """Get list of recommended models for ASCAES"""
    return {
        "generation_models": [
            {
                "name": "deepseek-r1:1.5b",
                "description": "Compact reasoning model, excellent for academic writing",
                "size": "Small (~3GB)",
                "recommended": True
            },
            {
                "name": "llama3:8b",
                "description": "Alternative model for text generation",
                "size": "Medium (~8GB)",
                "recommended": False
            }
        ],
        "embedding_models": [
            {
                "name": "nomic-embed-text",
                "description": "High-quality text embedding model for RAG",
                "size": "Small (~1GB)",
                "recommended": True
            },
            {
                "name": "all-minilm",
                "description": "Alternative embedding model",
                "size": "Small (~500MB)",
                "recommended": False
            }
        ]
    }
