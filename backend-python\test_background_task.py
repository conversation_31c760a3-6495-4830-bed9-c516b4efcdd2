#!/usr/bin/env python3
"""
Test script to debug background task execution
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.database import SessionLocal
from app.api.generation import run_generation_wrapper
from app.db.models import Document

def test_background_task():
    """Test the background task wrapper directly"""
    
    print("Testing background task wrapper...")

    # Create database session
    db = SessionLocal()

    try:
        # Get the latest document
        document = db.query(Document).order_by(Document.id.desc()).first()
        if not document:
            print("No documents found")
            return

        print(f"Found document: {document.title} (ID: {document.id})")

        # Test the background task wrapper
        print("Testing run_generation_wrapper...")
        run_generation_wrapper(document.id, db)

        print("Background task completed")

        # Check the document status
        db.refresh(document)
        print(f"Document status: {document.status}")
        print(f"Document progress: {document.progress}")

    except Exception as e:
        print(f"Background task failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()

if __name__ == "__main__":
    test_background_task()
