#!/usr/bin/env python3
"""
Test Ollama directly with the exact same code as the backend
"""

import httpx
import asyncio
import json

async def test_ollama_direct():
    """Test Ollama with the exact same code as the backend"""
    print("🔍 Testing Ollama directly with backend code...")
    print("=" * 50)
    
    try:
        print("Starting test generation...")
        async with httpx.AsyncClient() as client:
            print("Making request to Ollama...")
            response = await client.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": "deepseek-r1:1.5b",
                    "prompt": "Write a short paragraph about artificial intelligence.",
                    "stream": False
                },
                timeout=30.0
            )
            print(f"Response status: {response.status_code}")
            response.raise_for_status()
            result = response.json()
            print(f"Response data: {json.dumps(result, indent=2)}")
            
            content = result.get("response", "")
            print(f"\nExtracted content: {content}")
            print(f"Word count: {len(content.split())} words")
            
            return {
                "status": "success",
                "response": content,
                "model": result.get("model", "")
            }
    except Exception as e:
        print(f"Exception in test_ollama_generate: {e}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": str(e)}

def main():
    """Main test function"""
    result = asyncio.run(test_ollama_direct())
    
    print("\n" + "=" * 50)
    if result["status"] == "success":
        print("✅ Direct Ollama test successful!")
        print("✅ The issue is likely in the backend routing or async handling")
    else:
        print("❌ Direct Ollama test failed")
        print(f"Error: {result.get('message', 'Unknown error')}")

if __name__ == "__main__":
    main()
