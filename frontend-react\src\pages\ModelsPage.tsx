import React, { useState, useEffect } from 'react'
import { Brain, CheckCircle, XCircle, RefreshCw, Download, Trash2 } from 'lucide-react'

interface Model {
  name: string
  size: number
  modified_at: string
  digest: string
}

interface ModelsResponse {
  models: Model[]
  total: number
}

const ModelsPage: React.FC = () => {
  const [models, setModels] = useState<Model[]>([])
  const [loading, setLoading] = useState(true)
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'checking'>('checking')
  const [error, setError] = useState<string | null>(null)

  const checkConnection = async () => {
    setConnectionStatus('checking')
    try {
      const response = await fetch('http://127.0.0.1:8001/api/v1/models/check-connection')
      const data = await response.json()
      
      if (data.status === 'connected') {
        setConnectionStatus('connected')
        setError(null)
      } else {
        setConnectionStatus('disconnected')
        setError(data.message || 'Connection failed')
      }
    } catch (err) {
      setConnectionStatus('disconnected')
      setError('Failed to connect to backend')
    }
  }

  const fetchModels = async () => {
    setLoading(true)
    try {
      const response = await fetch('http://127.0.0.1:8001/api/v1/models')
      if (!response.ok) {
        throw new Error('Failed to fetch models')
      }
      const data: ModelsResponse = await response.json()
      setModels(data.models)
      setError(null)
    } catch (err) {
      setError('Failed to fetch models')
      console.error('Error fetching models:', err)
    } finally {
      setLoading(false)
    }
  }

  const testGeneration = async () => {
    try {
      const response = await fetch('http://127.0.0.1:8001/api/v1/models/test-generate', {
        method: 'POST'
      })
      const data = await response.json()
      
      if (data.status === 'success') {
        alert(`Generation test successful!\n\nGenerated content: ${data.response}`)
      } else {
        alert(`Generation test failed: ${data.message}`)
      }
    } catch (err) {
      alert('Failed to test generation')
    }
  }

  const formatFileSize = (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString()
  }

  useEffect(() => {
    checkConnection()
  }, [])

  useEffect(() => {
    if (connectionStatus === 'connected') {
      fetchModels()
    }
  }, [connectionStatus])

  return (
    <div className="space-y-6">
      {/* Connection Status */}
      <div className="bg-dark-panel rounded-lg p-6 border border-dark-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Brain className="w-6 h-6 text-dark-accent" />
            <div>
              <h3 className="text-lg font-semibold text-dark-text">Ollama Connection</h3>
              <p className="text-sm text-dark-text-secondary">Status of local AI model service</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              {connectionStatus === 'checking' && (
                <>
                  <RefreshCw className="w-5 h-5 text-yellow-500 animate-spin" />
                  <span className="text-yellow-500">Checking...</span>
                </>
              )}
              {connectionStatus === 'connected' && (
                <>
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-green-500">Connected</span>
                </>
              )}
              {connectionStatus === 'disconnected' && (
                <>
                  <XCircle className="w-5 h-5 text-red-500" />
                  <span className="text-red-500">Disconnected</span>
                </>
              )}
            </div>
            
            <button
              onClick={checkConnection}
              className="px-4 py-2 bg-dark-accent text-white rounded-lg hover:bg-opacity-80 transition-colors"
            >
              Refresh
            </button>
          </div>
        </div>
        
        {error && (
          <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}
      </div>

      {/* Models List */}
      <div className="bg-dark-panel rounded-lg border border-dark-border">
        <div className="p-6 border-b border-dark-border">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-dark-text">Available Models</h3>
              <p className="text-sm text-dark-text-secondary">Local AI models installed on your system</p>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={testGeneration}
                disabled={connectionStatus !== 'connected'}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Test Generation
              </button>
              <button
                onClick={fetchModels}
                disabled={connectionStatus !== 'connected'}
                className="px-4 py-2 bg-dark-accent text-white rounded-lg hover:bg-opacity-80 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <RefreshCw className="w-4 h-4 mr-2 inline" />
                Refresh
              </button>
            </div>
          </div>
        </div>

        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="w-6 h-6 text-dark-accent animate-spin mr-3" />
              <span className="text-dark-text-secondary">Loading models...</span>
            </div>
          ) : models.length === 0 ? (
            <div className="text-center py-8">
              <Brain className="w-12 h-12 text-dark-text-secondary mx-auto mb-4" />
              <p className="text-dark-text-secondary">No models found</p>
              <p className="text-sm text-dark-text-secondary mt-2">
                Install models using: <code className="bg-dark-border px-2 py-1 rounded">ollama pull model-name</code>
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {models.map((model, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 bg-dark-bg rounded-lg border border-dark-border"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-dark-accent rounded-lg flex items-center justify-center">
                      <Brain className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-medium text-dark-text">{model.name}</h4>
                      <div className="flex items-center space-x-4 text-sm text-dark-text-secondary">
                        <span>Size: {formatFileSize(model.size)}</span>
                        <span>Modified: {formatDate(model.modified_at)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {model.name.includes('deepseek-r1:1.5b') && (
                      <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">
                        Primary
                      </span>
                    )}
                    {model.name.includes('nomic-embed') && (
                      <span className="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full">
                        Embedding
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Model Information */}
      <div className="bg-dark-panel rounded-lg p-6 border border-dark-border">
        <h3 className="text-lg font-semibold text-dark-text mb-4">Model Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-dark-text mb-2">Primary Generation Model</h4>
            <p className="text-dark-text-secondary">deepseek-r1:1.5b - Used for document generation</p>
          </div>
          <div>
            <h4 className="font-medium text-dark-text mb-2">Embedding Model</h4>
            <p className="text-dark-text-secondary">nomic-embed-text:latest - Used for RAG context</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ModelsPage
