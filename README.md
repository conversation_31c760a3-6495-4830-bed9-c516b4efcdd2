# ASCAES - Academic Document Generation Specialist

A premium, offline-first desktop application for AI-powered academic document generation using local Ollama models with advanced RAG (Retrieval-Augmented Generation) capabilities.

## 🚀 Quick Start

**Two simple commands to get started:**

1. **First time setup**: `setup.bat` (installs dependencies)
2. **Run ASCAES**: `run-ascaes.bat` (starts the application)

**Required Ollama models:**
- `deepseek-r1:1.5b` (text generation)
- `nomic-embed-text` (embeddings)

## Features

- **100% Offline Operation**: All AI processing happens locally using Ollama
- **RAG-Powered Generation**: Maintains document coherence across long-form content (up to 1500 pages)
- **Advanced Writing Styles**: 8 distinct writing styles with cultural inflections
- **Academic Formatting**: Built-in support for citation styles and academic structure
- **Grammar Checking**: AI-powered grammar correction and improvement
- **Modern UI**: Professional "Helios" theme with dark/light mode support
- **Real-time Progress**: Live generation tracking with chapter-by-chapter progress

## Prerequisites

1. **Ollama**: Install Ollama from [https://ollama.ai](https://ollama.ai)
2. **Python 3.8+**: Required for the backend
3. **Node.js 16+**: Required for the frontend and Electron

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd ascaes-app
   ```

2. **Install dependencies**:
   ```bash
   npm run install:all
   ```

3. **Install required Ollama models**:
   ```bash
   # Generation model (choose one)
   ollama pull deepseek-r1:latest    # Recommended for best quality
   ollama pull llama3:8b             # Faster alternative
   
   # Embedding model (required for RAG)
   ollama pull nomic-embed-text
   ```

4. **Start the application**:
   ```bash
   npm run dev
   ```

## Usage

1. **Create a Document**: Use the settings panel to configure your document parameters
2. **Start Generation**: Click "Start Generation" to begin the AI writing process
3. **Monitor Progress**: Watch real-time progress as chapters are generated
4. **Review Content**: View and edit generated chapters
5. **Grammar Check**: Use the built-in grammar correction feature
6. **Export**: Download your completed document

## Architecture

### Backend (Python/FastAPI)
- **FastAPI**: REST API server
- **ChromaDB**: Local vector database for RAG
- **SQLAlchemy**: Document and chapter management
- **Ollama Integration**: Local AI model communication

### Frontend (React/TypeScript)
- **React 18**: Modern UI framework
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **Zustand**: State management
- **Vite**: Fast development and building

### Desktop Shell (Electron)
- **Electron**: Cross-platform desktop app
- **Process Management**: Automatic backend startup/shutdown
- **Native Menus**: Platform-specific application menus

## Configuration

### Writing Styles
- Analytical
- Instructional
- Reporting
- Argumentative/Persuasive
- Exploratory/Reflective
- Descriptive
- Narrative
- Schematic/Referential

### Cultural Inflections
- American
- Russian
- German
- Japanese
- French
- Italian

### Document Types
- Academic Paper
- Book
- Report

## Development

### Project Structure
```
ascaes-app/
├── backend-python/          # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core services (RAG, generation)
│   │   ├── db/             # Database models
│   │   └── main.py         # FastAPI app
│   └── requirements.txt
├── frontend-react/          # React frontend
│   ├── src/
│   │   ├── components/     # UI components
│   │   ├── pages/          # Page components
│   │   ├── store/          # Zustand store
│   │   └── App.tsx
│   └── package.json
├── electron/               # Electron main process
│   ├── main.js
│   └── preload.js
└── package.json           # Root package.json
```

### Development Commands
```bash
# Start all services in development mode
npm run dev

# Start individual services
npm run dev:frontend    # React dev server
npm run dev:backend     # Python FastAPI server
npm run dev:electron    # Electron app

# Build for production
npm run build
```

## Troubleshooting

### Ollama Connection Issues
- Ensure Ollama is running: `ollama serve`
- Check if models are installed: `ollama list`
- Verify Ollama is accessible at `http://localhost:11434`

### Backend Issues
- Check Python dependencies: `pip install -r backend-python/requirements.txt`
- Verify ChromaDB installation
- Check backend logs in the Electron console

### Frontend Issues
- Clear browser cache
- Check if backend is running on port 8000
- Verify all npm dependencies are installed

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please check the documentation or create an issue in the repository.
