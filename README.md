# ASCAES - Academic Document Specialist
## Production Ready Version

An AI-powered academic document generation system that creates high-quality research papers, essays, and academic content using local AI models through Ollama.

## 🚀 Features

- **📝 Multi-format Document Generation**: Create academic papers, research reports, essays, and more
- **🔒 Local AI Processing**: Uses Ollama for privacy-focused, offline AI generation
- **🌍 Dual Cultural Inflection**: Mix up to 2 cultural perspectives with 30-70% automatic balancing
- **📚 Academic Formatting**: Built-in support for APA, MLA, Chicago, and Harvard citation styles
- **⚡ Real-time Generation**: Watch your document being created chapter by chapter with PDF preview
- **📤 Export Options**: Export to PDF, Word, LaTeX, and plain text formats
- **🧠 RAG Integration**: Uses ChromaDB for context-aware content generation
- **📊 Analytics Dashboard**: Track generation statistics and document metrics

## 🛠 Tech Stack

- **Frontend**: React + TypeScript + Vite + Tailwind CSS
- **Backend**: FastAPI + Python + SQLite
- **AI Models**: Ollama (DeepSeek R1, Gemma 3, Nomic Embed)
- **Vector DB**: ChromaDB for RAG
- **Theme**: Helios <PERSON> UI

## ⚡ Quick Start

1. **Install Ollama**: Download from [ollama.ai](https://ollama.ai)
2. **Clone Repository**: `git clone <repository-url>`
3. **Start Application**: Double-click `start-ascaes.bat`

That's it! The application will automatically:
- Check Ollama connection
- Install required models (deepseek-r1:1.5b, nomic-embed-text, gemma3:1b)
- Start backend server (Port 8000)
- Start frontend server (Port 5173)

## 📋 Requirements

- **Ollama**: Must be installed and running
- **Python**: 3.8+ (auto-detected)
- **Node.js**: 16+ (auto-detected)
- **RAM**: 8GB+ recommended
- **Storage**: 5GB+ for models

## 🎯 Usage

1. **Open**: Navigate to `http://127.0.0.1:5173`
2. **Chat**: Enter document requirements in chat interface
3. **Configure**: Use settings panel for style, cultural inflection, formatting
4. **Generate**: Watch real-time chapter-by-chapter generation
5. **Preview**: View PDF-like document preview
6. **Export**: Download in PDF, Word, LaTeX, or TXT format

## ⚙️ Advanced Configuration

### Generation Parameters
- **Temperature**: 0.1-1.0 (creativity control)
- **Max Tokens**: 400-2000 per page
- **Top P/K**: Fine-tune generation quality
- **Model Keep-Alive**: 5m-1h persistence

### Cultural Inflection System
- **Single Culture**: 100% (e.g., American)
- **Dual Culture**: 30-70% balance (e.g., American 60% + German 40%)
- **Auto-balancing**: Sliders automatically complement each other
- **6 Cultures**: American, Russian, German, Japanese, French, Italian

### Academic Settings
- **Citation Styles**: APA, MLA, Chicago, Harvard
- **Academic Tone**: Formal, Semi-formal, Conversational
- **Grammar Check**: Automatic validation
- **Performance**: Connection timeouts, retry logic

## 📊 Dashboard Features

- **Total Documents**: Track all generated content
- **Completion Rate**: Success percentage with visual indicators
- **Word Statistics**: Total words, averages, trends
- **Recent Activity**: Last 7 days generation activity
- **Export Analytics**: Track download patterns

## 🏗 Architecture

```
ASCAES/
├── start-ascaes.bat         # Single start script
├── backend-python/          # FastAPI backend
│   ├── app/
│   │   ├── api/            # REST endpoints
│   │   ├── core/           # Generation service
│   │   ├── db/             # SQLite models
│   │   └── main.py         # FastAPI app
├── frontend-react/          # React SPA
│   ├── src/
│   │   ├── components/     # UI components
│   │   ├── pages/          # App pages
│   │   └── store/          # Zustand state
└── chroma_db/              # Vector database
```

## 🔧 Development

### Backend
```bash
cd backend-python
pip install -r requirements.txt
python -m uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
```

### Frontend
```bash
cd frontend-react
npm install
npm run dev
```

## 🎨 UI Features

- **Icon-only Interface**: Clean, minimal controls
- **Top-right Controls**: Preview, settings, export dropdown
- **PDF-like Preview**: Real-time document view with formatting
- **Stats Dashboard**: Visual analytics and metrics
- **Responsive Design**: Works on all screen sizes
- **Dark Theme**: Helios design system

## 🚀 Production Ready

- ✅ **Single Start Script**: One-click deployment
- ✅ **Auto Model Management**: Automatic model installation
- ✅ **Connection Persistence**: Stable Ollama connections
- ✅ **Error Handling**: Comprehensive retry logic
- ✅ **Performance Optimized**: Fast generation with quality control
- ✅ **Clean Codebase**: Removed unnecessary files
- ✅ **User-friendly**: Intuitive interface design

## 📄 License

MIT License - Open source and free to use

---

**ASCAES** - Professional academic writing powered by local AI 🎓