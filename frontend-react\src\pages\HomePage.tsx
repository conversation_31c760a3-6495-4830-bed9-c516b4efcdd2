import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { FileText, Plus, BookOpen, TrendingUp } from 'lucide-react'
import { useAppStore } from '../store/useAppStore'

const HomePage: React.FC = () => {
  const navigate = useNavigate()
  const {
    documents,
    error,
    fetchDocuments,
    setError
  } = useAppStore()

  useEffect(() => {
    fetchDocuments()
  }, [fetchDocuments])
  
  const recentDocuments = documents.slice(0, 3)
  const completedDocuments = documents.filter(doc => doc.status === 'completed')
  const inProgressDocuments = documents.filter(doc => doc.status === 'generating')
  
  return (
    <div className="space-y-8">
      {/* Error Display */}
      {error && (
        <div className="bg-dark-error/10 border border-dark-error/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-dark-error text-sm">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-dark-error hover:text-dark-error/80"
            >
              ×
            </button>
          </div>
        </div>
      )}
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-dark-text-secondary text-sm">Total Documents</p>
              <p className="text-2xl font-bold text-dark-text">{documents.length}</p>
            </div>
            <div className="w-12 h-12 bg-dark-accent/10 rounded-lg flex items-center justify-center">
              <FileText className="w-6 h-6 text-dark-accent" />
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-dark-text-secondary text-sm">Completed</p>
              <p className="text-2xl font-bold text-dark-text">{completedDocuments.length}</p>
            </div>
            <div className="w-12 h-12 bg-dark-success/10 rounded-lg flex items-center justify-center">
              <BookOpen className="w-6 h-6 text-dark-success" />
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-dark-text-secondary text-sm">In Progress</p>
              <p className="text-2xl font-bold text-dark-text">{inProgressDocuments.length}</p>
            </div>
            <div className="w-12 h-12 bg-dark-warning/10 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-dark-warning" />
            </div>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Quick Actions */}
        <div>
          <h3 className="text-lg font-semibold text-dark-text mb-4">Quick Actions</h3>
          <div className="space-y-4">
            <button
              onClick={() => navigate('/chat')}
              className="w-full btn-primary flex items-center justify-center space-x-3 py-4"
            >
              <Plus className="w-5 h-5" />
              <span>Start New Document with AI Chat</span>
            </button>

            <div className="text-center">
              <p className="text-sm text-dark-text-secondary mb-2">or</p>
              <button
                onClick={() => navigate('/documents')}
                className="btn-secondary"
              >
                View Existing Documents
              </button>
            </div>
          </div>
        </div>
        
        {/* Recent Documents */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-dark-text">Recent Documents</h3>
            <button
              onClick={() => navigate('/documents')}
              className="text-dark-accent hover:text-dark-accent-hover text-sm font-medium"
            >
              View All
            </button>
          </div>
          
          <div className="space-y-4">
            {recentDocuments.length > 0 ? (
              recentDocuments.map((doc) => (
                <div
                  key={doc.id}
                  onClick={() => navigate(`/documents/${doc.id}`)}
                  className="card hover:bg-dark-border/50 cursor-pointer transition-colors duration-200"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-dark-text truncate">{doc.title}</h4>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className="text-xs text-dark-text-secondary">
                          {doc.document_type}
                        </span>
                        <span className="text-xs text-dark-text-secondary">
                          {doc.pages} pages
                        </span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          doc.status === 'completed' 
                            ? 'bg-dark-success/10 text-dark-success'
                            : doc.status === 'generating'
                            ? 'bg-dark-warning/10 text-dark-warning'
                            : 'bg-dark-text-secondary/10 text-dark-text-secondary'
                        }`}>
                          {doc.status}
                        </span>
                      </div>
                    </div>
                    {doc.status === 'generating' && (
                      <div className="ml-4">
                        <div className="w-8 h-8 rounded-full border-2 border-dark-accent border-t-transparent animate-spin" />
                      </div>
                    )}
                  </div>
                  
                  {doc.status === 'generating' && (
                    <div className="mt-3">
                      <div className="flex items-center justify-between text-xs text-dark-text-secondary mb-1">
                        <span>Progress</span>
                        <span>{Math.round(doc.progress)}%</span>
                      </div>
                      <div className="w-full bg-dark-border rounded-full h-2">
                        <div 
                          className="bg-dark-accent h-2 rounded-full transition-all duration-300"
                          style={{ width: `${doc.progress}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="card text-center py-8">
                <FileText className="w-12 h-12 text-dark-text-secondary mx-auto mb-4" />
                <p className="text-dark-text-secondary">No documents yet</p>
                <p className="text-sm text-dark-text-secondary mt-1">
                  Create your first document to get started
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default HomePage
