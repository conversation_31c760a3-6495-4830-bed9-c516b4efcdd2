@echo off
title ASCAES - Academic Document Specialist
color 0A

echo.
echo ========================================
echo    ASCAES - Academic Document Specialist
echo    Production Ready Version
echo ========================================
echo.

:: Check if Ollama is running
echo [1/4] Checking Ollama service...
curl -s http://127.0.0.1:11434/api/version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Ollama is not running. Please start Ollama first.
    echo    Run: ollama serve
    pause
    exit /b 1
)
echo ✅ Ollama is running

:: Check required models
echo [2/4] Checking required models...
ollama list | findstr "deepseek-r1:1.5b" >nul
if %errorlevel% neq 0 (
    echo ❌ DeepSeek model not found. Installing...
    ollama pull deepseek-r1:1.5b
)

ollama list | findstr "nomic-embed-text:latest" >nul
if %errorlevel% neq 0 (
    echo ❌ Embedding model not found. Installing...
    ollama pull nomic-embed-text:latest
)

ollama list | findstr "gemma3:1b" >nul
if %errorlevel% neq 0 (
    echo ❌ Fallback model not found. Installing...
    ollama pull gemma3:1b
)
echo ✅ All models ready

:: Check Python dependencies
echo [3/4] Checking Python dependencies...
cd backend-python
python -c "import fastapi, uvicorn, sqlalchemy; print('✅ Python dependencies OK')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Installing Python dependencies...
    pip install -r requirements.txt
)
cd ..

:: Check Node.js dependencies
echo [4/4] Checking Node.js dependencies...
cd frontend-react
if not exist "node_modules" (
    echo ❌ Installing Node.js dependencies...
    npm install
) else (
    echo ✅ Node.js dependencies OK
)
cd ..

:: Start backend
echo [5/6] Starting backend server...
cd backend-python
start "ASCAES Backend" cmd /k "echo Starting ASCAES Backend... && python -m uvicorn app.main:app --host 127.0.0.1 --port 8002 --reload"
cd ..

:: Wait for backend to start
echo Waiting for backend to initialize...
timeout /t 5 /nobreak >nul

:: Start frontend
echo [6/6] Starting frontend...
cd frontend-react
start "ASCAES Frontend" cmd /k "npm run dev"
cd ..

:: Wait for services to start
echo Waiting for services to initialize...
timeout /t 8 /nobreak >nul

:: Open browser automatically
echo [7/7] Opening ASCAES in browser...
start http://127.0.0.1:5173

echo.
echo ========================================
echo    ASCAES is now running!
echo.
echo    Frontend: http://127.0.0.1:5173
echo    Backend:  http://127.0.0.1:8002
echo    Ollama:   http://127.0.0.1:11434
echo.
echo    The application has opened in your browser.
echo    Close this window to stop all services.
echo ========================================
echo.

:: Keep the main window open and monitor services
echo Services are running in background windows.
echo Close this window to stop all services.
echo.
:loop
timeout /t 300 /nobreak >nul
echo [%time%] Services still running... (Close this window to stop)
goto loop
