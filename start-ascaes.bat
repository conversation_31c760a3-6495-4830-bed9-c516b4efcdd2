@echo off
title ASCAES - Academic Document Specialist
color 0A

echo.
echo ========================================
echo    ASCAES - Academic Document Specialist
echo    Production Ready Version
echo ========================================
echo.

:: Check if Ollama is running
echo [1/4] Checking Ollama service...
curl -s http://127.0.0.1:11434/api/version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Ollama is not running. Please start Ollama first.
    echo    Run: ollama serve
    pause
    exit /b 1
)
echo ✅ Ollama is running

:: Check required models
echo [2/4] Checking required models...
ollama list | findstr "deepseek-r1:1.5b" >nul
if %errorlevel% neq 0 (
    echo ❌ DeepSeek model not found. Installing...
    ollama pull deepseek-r1:1.5b
)

ollama list | findstr "nomic-embed-text:latest" >nul
if %errorlevel% neq 0 (
    echo ❌ Embedding model not found. Installing...
    ollama pull nomic-embed-text:latest
)

ollama list | findstr "gemma3:1b" >nul
if %errorlevel% neq 0 (
    echo ❌ Fallback model not found. Installing...
    ollama pull gemma3:1b
)
echo ✅ All models ready

:: Start backend
echo [3/4] Starting backend server...
cd backend-python
start "ASCAES Backend" cmd /k "python -m uvicorn app.main:app --host 127.0.0.1 --port 8000"
cd ..

:: Wait for backend to start
echo Waiting for backend to initialize...
timeout /t 5 /nobreak >nul

:: Start frontend
echo [4/4] Starting frontend...
cd frontend-react
start "ASCAES Frontend" cmd /k "npm run dev"
cd ..

echo.
echo ========================================
echo    ASCAES is starting up...
echo    
echo    Frontend: http://127.0.0.1:5173
echo    Backend:  http://127.0.0.1:8000
echo    Ollama:   http://127.0.0.1:11434
echo    
echo    Close this window to stop all services
echo ========================================
echo.

:: Keep the main window open
:loop
timeout /t 30 /nobreak >nul
goto loop
