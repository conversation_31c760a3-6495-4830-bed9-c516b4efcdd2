import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { 
  FileText, 
  Plus, 
  Search,
  Filter,
  Trash2,
  Eye,
  Calendar,
  BookOpen,
  Download,
  BarChart3,
  TrendingUp,
  Users,
  Zap,
  Clock
} from 'lucide-react'
import { useAppStore } from '../store/useAppStore'

const DocumentsPage: React.FC = () => {
  const navigate = useNavigate()
  const { 
    documents, 
    isLoading, 
    error, 
    fetchDocuments, 
    deleteDocument,
    setError 
  } = useAppStore()
  
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [typeFilter, setTypeFilter] = useState('all')
  
  useEffect(() => {
    fetchDocuments()
  }, [fetchDocuments])
  
  const handleDeleteDocument = async (id: number, title: string) => {
    if (window.confirm(`Are you sure you want to delete "${title}"?`)) {
      await deleteDocument(id)
    }
  }

  const handleDownloadPDF = async (id: number, title: string) => {
    try {
      const response = await fetch(`http://127.0.0.1:8001/api/v1/documents/${id}/export-pdf`)
      if (!response.ok) throw new Error('Failed to download PDF')

      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${title.replace(/[^a-z0-9]/gi, '_')}.pdf`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      setError('Failed to download PDF')
    }
  }
  
  // Calculate stats
  const stats = {
    totalDocuments: documents.length,
    completedDocuments: documents.filter(doc => doc.status === 'completed').length,
    totalWords: documents.reduce((sum, doc) => sum + (doc.estimated_words || 0), 0),
    totalPages: documents.reduce((sum, doc) => sum + (doc.pages || 0), 0),
    averageWordsPerDoc: documents.length > 0 ? Math.round(documents.reduce((sum, doc) => sum + (doc.estimated_words || 0), 0) / documents.length) : 0,
    recentActivity: documents.filter(doc => {
      const docDate = new Date(doc.created_at)
      const weekAgo = new Date()
      weekAgo.setDate(weekAgo.getDate() - 7)
      return docDate >= weekAgo
    }).length
  }

  // Filter documents
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || doc.status === statusFilter
    const matchesType = typeFilter === 'all' || doc.document_type === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-dark-success/10 text-dark-success'
      case 'generating':
        return 'bg-dark-warning/10 text-dark-warning'
      case 'error':
        return 'bg-dark-error/10 text-dark-error'
      default:
        return 'bg-dark-text-secondary/10 text-dark-text-secondary'
    }
  }
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }
  
  return (
    <div className="space-y-6">
      {/* Error Display */}
      {error && (
        <div className="bg-dark-error/10 border border-dark-error/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-dark-error text-sm">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-dark-error hover:text-dark-error/80"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Stats Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-dark-panel rounded-lg p-4 border border-dark-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-dark-text-secondary text-sm">Total Documents</p>
              <p className="text-2xl font-bold text-dark-text">{stats.totalDocuments}</p>
            </div>
            <FileText className="w-8 h-8 text-dark-accent" />
          </div>
        </div>

        <div className="bg-dark-panel rounded-lg p-4 border border-dark-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-dark-text-secondary text-sm">Completed</p>
              <p className="text-2xl font-bold text-green-400">{stats.completedDocuments}</p>
              <p className="text-xs text-dark-text-secondary">
                {stats.totalDocuments > 0 ? Math.round((stats.completedDocuments / stats.totalDocuments) * 100) : 0}% success rate
              </p>
            </div>
            <BarChart3 className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className="bg-dark-panel rounded-lg p-4 border border-dark-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-dark-text-secondary text-sm">Total Words</p>
              <p className="text-2xl font-bold text-dark-text">{stats.totalWords.toLocaleString()}</p>
              <p className="text-xs text-dark-text-secondary">
                Avg: {stats.averageWordsPerDoc.toLocaleString()} per doc
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-blue-400" />
          </div>
        </div>

        <div className="bg-dark-panel rounded-lg p-4 border border-dark-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-dark-text-secondary text-sm">Recent Activity</p>
              <p className="text-2xl font-bold text-dark-text">{stats.recentActivity}</p>
              <p className="text-xs text-dark-text-secondary">
                Last 7 days
              </p>
            </div>
            <Zap className="w-8 h-8 text-yellow-400" />
          </div>
        </div>
      </div>
      
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-dark-text-secondary" />
            <input
              type="text"
              placeholder="Search documents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-field pl-10 w-64"
            />
          </div>
          
          {/* Filters */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="select-field"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="generating">Generating</option>
            <option value="completed">Completed</option>
            <option value="error">Error</option>
          </select>
          
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="select-field"
          >
            <option value="all">All Types</option>
            <option value="Academic Paper">Academic Paper</option>
            <option value="Book">Book</option>
            <option value="Report">Report</option>
          </select>
        </div>
        
        <button
          onClick={() => navigate('/')}
          className="btn-primary flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>New Document</span>
        </button>
      </div>
      
      {/* Documents Grid */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="w-8 h-8 rounded-full border-2 border-dark-accent border-t-transparent animate-spin" />
        </div>
      ) : filteredDocuments.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDocuments.map((doc) => (
            <div key={doc.id} className="card hover:bg-dark-border/30 transition-colors duration-200">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="font-semibold text-dark-text truncate mb-2">{doc.title}</h3>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(doc.status)}`}>
                      {doc.status}
                    </span>
                    <span className="text-xs text-dark-text-secondary">
                      {doc.document_type}
                    </span>
                  </div>
                </div>
                
                {doc.status === 'generating' && (
                  <div className="w-6 h-6 rounded-full border-2 border-dark-accent border-t-transparent animate-spin ml-2" />
                )}
              </div>
              
              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-dark-text-secondary">Pages:</span>
                  <span className="text-dark-text">{doc.pages}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-dark-text-secondary">Words:</span>
                  <span className="text-dark-text">{doc.estimated_words.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-dark-text-secondary">Created:</span>
                  <span className="text-dark-text">{formatDate(doc.created_at)}</span>
                </div>
              </div>
              
              {doc.status === 'generating' && (
                <div className="mb-4">
                  <div className="flex items-center justify-between text-xs text-dark-text-secondary mb-1">
                    <span>Progress</span>
                    <span>{Math.round(doc.progress)}%</span>
                  </div>
                  <div className="w-full bg-dark-border rounded-full h-2">
                    <div 
                      className="bg-dark-accent h-2 rounded-full transition-all duration-300"
                      style={{ width: `${doc.progress}%` }}
                    />
                  </div>
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => navigate(`/documents/${doc.id}`)}
                  className="btn-secondary flex-1 flex items-center justify-center space-x-2"
                >
                  <Eye className="w-4 h-4" />
                  <span>View</span>
                </button>

                {doc.status === 'completed' && (
                  <button
                    onClick={() => handleDownloadPDF(doc.id, doc.title)}
                    className="btn-secondary px-3"
                    title="Download PDF"
                  >
                    <Download className="w-4 h-4" />
                  </button>
                )}

                <button
                  onClick={() => handleDeleteDocument(doc.id, doc.title)}
                  className="btn-secondary text-dark-error hover:bg-dark-error/10 px-3"
                  title="Delete document"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 text-dark-text-secondary mx-auto mb-4" />
          <h3 className="text-lg font-medium text-dark-text mb-2">
            {searchTerm || statusFilter !== 'all' || typeFilter !== 'all' 
              ? 'No documents match your filters' 
              : 'No documents yet'
            }
          </h3>
          <p className="text-dark-text-secondary mb-6">
            {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Create your first document to get started'
            }
          </p>
          <button
            onClick={() => navigate('/')}
            className="btn-primary flex items-center space-x-2 mx-auto"
          >
            <Plus className="w-4 h-4" />
            <span>Create Document</span>
          </button>
        </div>
      )}
    </div>
  )
}

export default DocumentsPage
