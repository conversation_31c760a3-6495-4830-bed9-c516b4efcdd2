#!/usr/bin/env python3
"""
Quick test to verify the fixes are working
"""

import requests
import json
import time

def test_reasoning_filter():
    """Test the reasoning filter function"""
    from backend_python.app.core.generation_service import GenerationService
    
    service = GenerationService()
    
    # Test content with reasoning
    test_content = """
    <think>
    I need to write about AI. Let me think about this...
    </think>
    
    Alright, I need to create a detailed chapter outline for an academic paper titled "AI" with specific criteria.
    
    Artificial Intelligence (AI) represents one of the most transformative technologies of our time. This field encompasses machine learning, neural networks, and cognitive computing systems that can perform tasks traditionally requiring human intelligence.
    
    The development of AI has progressed through several distinct phases, from early rule-based systems to modern deep learning architectures.
    """
    
    filtered = service.filter_reasoning_content(test_content)
    print("Original content length:", len(test_content))
    print("Filtered content length:", len(filtered))
    print("\nFiltered content:")
    print(filtered)
    print("\n" + "="*50)

def test_ollama_connection():
    """Test Ollama connection"""
    try:
        response = requests.get("http://localhost:11434/api/version", timeout=5)
        response.raise_for_status()
        print("✅ Ollama connection: OK")
        return True
    except Exception as e:
        print(f"❌ Ollama connection failed: {e}")
        return False

def test_backend_api():
    """Test backend API"""
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/models/check-connection", timeout=5)
        if response.status_code == 200:
            print("✅ Backend API: OK")
            return True
        else:
            print(f"❌ Backend API returned: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend API failed: {e}")
        return False

def test_generation_prompt():
    """Test if generation produces proper word count"""
    try:
        response = requests.post(
            "http://localhost:11434/api/chat",
            json={
                "model": "deepseek-r1:1.5b",
                "messages": [
                    {
                        "role": "user",
                        "content": """Write a complete chapter of approximately 250 words (minimum 250 words) about artificial intelligence. 
                        
                        IMPORTANT:
                        - Start writing the chapter content immediately
                        - Do not include any thinking, reasoning, or planning
                        - Do not include meta-commentary about the task
                        - Write only the actual chapter content
                        - Begin with the first paragraph of the chapter"""
                    }
                ],
                "stream": False
            },
            timeout=30
        )
        response.raise_for_status()
        result = response.json()
        content = result.get("message", {}).get("content", "")
        
        word_count = len(content.split())
        print(f"✅ Generated content: {word_count} words")
        
        if word_count >= 200:  # Allow some flexibility
            print("✅ Word count is adequate")
        else:
            print("⚠️ Word count might be too low")
            
        # Check for reasoning content
        if any(phrase in content.lower() for phrase in ["alright", "i need to", "let me", "first, i'll"]):
            print("⚠️ Content might contain reasoning")
        else:
            print("✅ Content appears clean of reasoning")
            
        return True
        
    except Exception as e:
        print(f"❌ Generation test failed: {e}")
        return False

def main():
    print("🔍 Testing ASCAES Fixes...")
    print("=" * 50)
    
    # Test 1: Reasoning filter
    print("1. Testing reasoning filter...")
    try:
        test_reasoning_filter()
    except Exception as e:
        print(f"❌ Reasoning filter test failed: {e}")
    
    print("\n2. Testing Ollama connection...")
    ollama_ok = test_ollama_connection()
    
    print("\n3. Testing backend API...")
    backend_ok = test_backend_api()
    
    if ollama_ok:
        print("\n4. Testing generation quality...")
        test_generation_prompt()
    else:
        print("\n4. Skipping generation test (Ollama not available)")
    
    print("\n" + "=" * 50)
    print("🎉 Fix testing completed!")
    
    if ollama_ok and backend_ok:
        print("✅ All systems appear to be working")
        print("✅ Document generation should work correctly")
    else:
        print("⚠️ Some issues detected - check the output above")

if __name__ == "__main__":
    main()
