import React, { useState, useEffect } from 'react'
import { useAppStore } from '../store/useAppStore'

interface ChatSettingsPanelProps {
  onSubmit: (data: any) => void
  isLoading?: boolean
}

const ChatSettingsPanel: React.FC<ChatSettingsPanelProps> = ({ onSubmit, isLoading = false }) => {
  const { getWordCountEstimate } = useAppStore()
  
  // Form state
  const [formData, setFormData] = useState({
    title: '',
    document_type: 'Academic Paper',
    spacing: 'Double',
    pages: 10,
    improve_realism: true,
    enable_grammar_check: true,
    writing_style: 'Analytical',
    cultural_inflection: 'American',
    academic_formatting: {
      enable_academic_formatting: false,
      citation_style: 'APA',
      student_name: '',
      course_name: '',
      instructor_name: '',
      due_date: ''
    }
  })
  
  const [estimatedWords, setEstimatedWords] = useState(0)
  
  // Update word count estimate when relevant fields change
  useEffect(() => {
    const updateWordCount = async () => {
      try {
        const estimate = await getWordCountEstimate(
          formData.pages,
          formData.document_type,
          formData.spacing
        )
        setEstimatedWords(estimate)
      } catch (error) {
        console.error('Error getting word count estimate:', error)
      }
    }
    
    updateWordCount()
  }, [formData.pages, formData.document_type, formData.spacing, getWordCountEstimate])
  
  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith('academic_formatting.')) {
      const subField = field.replace('academic_formatting.', '')
      setFormData(prev => ({
        ...prev,
        academic_formatting: {
          ...prev.academic_formatting,
          [subField]: value
        }
      }))
    } else {
      setFormData(prev => ({ ...prev, [field]: value }))
    }
  }
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }
  
  const documentTypes = ['Academic Paper', 'Book', 'Report']
  const spacingOptions = ['Double', 'Single']
  const writingStyles = [
    'Analytical',
    'Instructional', 
    'Reporting',
    'Argumentative / Persuasive',
    'Exploratory / Reflective',
    'Descriptive',
    'Narrative',
    'Schematic / Referential'
  ]
  const culturalInflections = [
    'American',
    'Russian',
    'German',
    'Japanese',
    'French',
    'Italian'
  ]
  const citationStyles = ['APA', 'MLA', 'Chicago', 'Harvard', 'IEEE']
  
  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Settings */}
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-dark-text mb-2">
                Document Type
              </label>
              <select
                value={formData.document_type}
                onChange={(e) => handleInputChange('document_type', e.target.value)}
                className="select-field w-full"
              >
                {documentTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-dark-text mb-2">
                Spacing
              </label>
              <select
                value={formData.spacing}
                onChange={(e) => handleInputChange('spacing', e.target.value)}
                className="select-field w-full"
              >
                {spacingOptions.map(spacing => (
                  <option key={spacing} value={spacing}>{spacing}</option>
                ))}
              </select>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-dark-text mb-2">
              Pages (max 1500)
            </label>
            <input
              type="number"
              min="1"
              max="1500"
              value={formData.pages}
              onChange={(e) => handleInputChange('pages', parseInt(e.target.value) || 1)}
              className="input-field w-full"
            />
            <p className="text-sm text-dark-text-secondary mt-1">
              Approx. words: {estimatedWords.toLocaleString()}
            </p>
          </div>
        </div>
        
        {/* Writing Style & Cultural Inflection */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-dark-text mb-2">
              Writing Style
            </label>
            <select
              value={formData.writing_style}
              onChange={(e) => handleInputChange('writing_style', e.target.value)}
              className="select-field w-full"
            >
              {writingStyles.map(style => (
                <option key={style} value={style}>{style}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-dark-text mb-2">
              Cultural Inflection
            </label>
            <select
              value={formData.cultural_inflection}
              onChange={(e) => handleInputChange('cultural_inflection', e.target.value)}
              className="select-field w-full"
            >
              {culturalInflections.map(culture => (
                <option key={culture} value={culture}>{culture}</option>
              ))}
            </select>
          </div>
        </div>
        
        {/* Toggles */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-dark-text">
                Improve realism (Humanization)
              </label>
              <p className="text-xs text-dark-text-secondary">
                Apply humanization protocols to make writing more natural
              </p>
            </div>
            <button
              type="button"
              onClick={() => handleInputChange('improve_realism', !formData.improve_realism)}
              className={`toggle-switch ${formData.improve_realism ? 'enabled' : 'disabled'}`}
            >
              <span className={`toggle-thumb ${formData.improve_realism ? 'enabled' : 'disabled'}`} />
            </button>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-dark-text">
                Enable grammatical error checking
              </label>
              <p className="text-xs text-dark-text-secondary">
                Show grammar check button on generated text
              </p>
            </div>
            <button
              type="button"
              onClick={() => handleInputChange('enable_grammar_check', !formData.enable_grammar_check)}
              className={`toggle-switch ${formData.enable_grammar_check ? 'enabled' : 'disabled'}`}
            >
              <span className={`toggle-thumb ${formData.enable_grammar_check ? 'enabled' : 'disabled'}`} />
            </button>
          </div>
        </div>
        
        {/* Academic Formatting Section */}
        <div className="border-t border-dark-border pt-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <label className="text-sm font-medium text-dark-text">
                Academic Formatting
              </label>
              <p className="text-xs text-dark-text-secondary">
                Enable academic paper formatting with citations
              </p>
            </div>
            <button
              type="button"
              onClick={() => handleInputChange('academic_formatting.enable_academic_formatting', !formData.academic_formatting.enable_academic_formatting)}
              className={`toggle-switch ${formData.academic_formatting.enable_academic_formatting ? 'enabled' : 'disabled'}`}
            >
              <span className={`toggle-thumb ${formData.academic_formatting.enable_academic_formatting ? 'enabled' : 'disabled'}`} />
            </button>
          </div>
          
          {formData.academic_formatting.enable_academic_formatting && (
            <div className="space-y-4 animate-slide-in">
              <div>
                <label className="block text-sm font-medium text-dark-text mb-2">
                  Citation Style
                </label>
                <select
                  value={formData.academic_formatting.citation_style}
                  onChange={(e) => handleInputChange('academic_formatting.citation_style', e.target.value)}
                  className="select-field w-full"
                >
                  {citationStyles.map(style => (
                    <option key={style} value={style}>{style}</option>
                  ))}
                </select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-dark-text mb-2">
                    Student Name
                  </label>
                  <input
                    type="text"
                    value={formData.academic_formatting.student_name}
                    onChange={(e) => handleInputChange('academic_formatting.student_name', e.target.value)}
                    className="input-field w-full"
                    placeholder="Your name..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-dark-text mb-2">
                    Course Name
                  </label>
                  <input
                    type="text"
                    value={formData.academic_formatting.course_name}
                    onChange={(e) => handleInputChange('academic_formatting.course_name', e.target.value)}
                    className="input-field w-full"
                    placeholder="Course name..."
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-dark-text mb-2">
                    Instructor Name
                  </label>
                  <input
                    type="text"
                    value={formData.academic_formatting.instructor_name}
                    onChange={(e) => handleInputChange('academic_formatting.instructor_name', e.target.value)}
                    className="input-field w-full"
                    placeholder="Instructor name..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-dark-text mb-2">
                    Due Date
                  </label>
                  <input
                    type="date"
                    value={formData.academic_formatting.due_date}
                    onChange={(e) => handleInputChange('academic_formatting.due_date', e.target.value)}
                    className="input-field w-full"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Submit Button */}
        <div className="pt-6">
          <button
            type="submit"
            disabled={isLoading}
            className="btn-primary w-full"
          >
            {isLoading ? 'Saving Settings...' : 'Save Settings'}
          </button>
        </div>
      </form>
    </div>
  )
}

export default ChatSettingsPanel
