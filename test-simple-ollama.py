#!/usr/bin/env python3
"""
Simple test to see if we can get ANY response from Ollama
"""

import requests
import json
import time

def test_simple_generate():
    """Test with the old generate API and very simple prompt"""
    print("Testing simple generate API...")
    
    url = "http://localhost:11434/api/generate"
    payload = {
        "model": "deepseek-r1:1.5b",
        "prompt": "Hello",
        "stream": False,
        "options": {
            "num_predict": 50  # Very short response
        }
    }
    
    try:
        print("Sending simple request...")
        response = requests.post(url, json=payload, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success! Response: {result}")
            return True
        else:
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"Failed: {e}")
        return False

def test_model_info():
    """Get model info"""
    try:
        response = requests.post(
            "http://localhost:11434/api/show",
            json={"name": "deepseek-r1:1.5b"},
            timeout=10
        )
        if response.status_code == 200:
            info = response.json()
            print(f"Model info: {json.dumps(info, indent=2)}")
        else:
            print(f"Model info failed: {response.text}")
    except Exception as e:
        print(f"Model info error: {e}")

def main():
    print("🔍 Simple Ollama Test...")
    print("=" * 30)
    
    # Test model info
    test_model_info()
    print()
    
    # Test simple generation
    success = test_simple_generate()
    
    print("\n" + "=" * 30)
    if success:
        print("✅ Basic generation works!")
    else:
        print("❌ Generation failed")
        print("\nTrying to restart Ollama might help:")
        print("1. Stop: taskkill /f /im ollama.exe")
        print("2. Start: ollama serve")

if __name__ == "__main__":
    main()
