import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  FileText,
  Home,
  Settings,
  Moon,
  Sun,
  BookOpen,
  MessageSquare
} from 'lucide-react'
import { useAppStore } from '../store/useAppStore'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation()
  const { isDarkMode, toggleTheme } = useAppStore()
  
  const navItems = [
    { path: '/', icon: Home, label: 'Home' },
    { path: '/chat', icon: MessageSquare, label: 'Chat' },
    { path: '/documents', icon: FileText, label: 'Documents' },
    { path: '/settings', icon: Settings, label: 'Settings' }
  ]
  
  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(path)
  }
  
  return (
    <div className="flex h-screen bg-dark-bg">
      {/* Sidebar */}
      <div className="w-64 bg-dark-panel border-r border-dark-border flex flex-col">
        {/* Logo/Brand */}
        <div className="p-6 border-b border-dark-border">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-dark-accent rounded-lg flex items-center justify-center">
              <BookOpen className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-dark-text">ASCAES</h1>
              <p className="text-xs text-dark-text-secondary">Academic Document Specialist</p>
            </div>
          </div>
        </div>
        
        {/* Navigation */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {navItems.map((item) => {
              const Icon = item.icon
              return (
                <li key={item.path}>
                  <Link
                    to={item.path}
                    className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors duration-200 ${
                      isActive(item.path)
                        ? 'bg-dark-accent text-white'
                        : 'text-dark-text-secondary hover:text-dark-text hover:bg-dark-border'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{item.label}</span>
                  </Link>
                </li>
              )
            })}
          </ul>
        </nav>
        
        {/* Theme Toggle */}
        <div className="p-4 border-t border-dark-border">
          <button
            onClick={toggleTheme}
            className="flex items-center space-x-3 px-3 py-2 rounded-lg text-dark-text-secondary hover:text-dark-text hover:bg-dark-border transition-colors duration-200 w-full"
          >
            {isDarkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
            <span className="font-medium">
              {isDarkMode ? 'Light Mode' : 'Dark Mode'}
            </span>
          </button>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-dark-panel border-b border-dark-border px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-dark-text">
                {location.pathname === '/' && 'Dashboard'}
                {location.pathname === '/chat' && 'AI Chat'}
                {location.pathname === '/documents' && 'Documents'}
                {location.pathname.startsWith('/documents/') && 'Document View'}
                {location.pathname === '/settings' && 'Settings'}
              </h2>
              <p className="text-sm text-dark-text-secondary mt-1">
                {location.pathname === '/' && 'Create and manage your academic documents'}
                {location.pathname === '/chat' && 'Chat with AI to generate documents'}
                {location.pathname === '/documents' && 'View and manage all your documents'}
                {location.pathname.startsWith('/documents/') && 'View document details and chapters'}
                {location.pathname === '/settings' && 'Configure application settings'}
              </p>
            </div>
          </div>
        </header>
        
        {/* Page Content */}
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  )
}

export default Layout
