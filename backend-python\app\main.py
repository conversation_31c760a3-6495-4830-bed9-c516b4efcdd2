"""
ASCAES Backend - FastAPI Application
Academic Document Generation Specialist
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os

from app.api import generation, documents, models
from app.db.database import init_db

# Initialize FastAPI app
app = FastAPI(
    title="ASCAES Backend",
    description="Academic Document Generation Specialist - AI-Powered Writing Tool",
    version="1.0.0"
)

# Configure CORS for Electron frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(generation.router, prefix="/api/v1", tags=["generation"])
app.include_router(documents.router, prefix="/api/v1", tags=["documents"])
app.include_router(models.router, prefix="/api/v1", tags=["models"])

@app.on_event("startup")
async def startup_event():
    """Initialize database and services on startup"""
    await init_db()

@app.get("/")
async def root():
    return {"message": "ASCAES Backend API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "ASCAES Backend"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
