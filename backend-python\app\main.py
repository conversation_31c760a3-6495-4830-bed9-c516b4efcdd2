"""
ASCAES Backend - FastAPI Application
Academic Document Generation Specialist
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os

from app.api import generation, documents, models
from app.db.database import init_db

# Initialize FastAPI app
app = FastAPI(
    title="ASCAES Backend",
    description="Academic Document Generation Specialist - AI-Powered Writing Tool",
    version="1.0.0"
)

# Configure CORS for Electron frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(generation.router, prefix="/api/v1", tags=["generation"])
app.include_router(documents.router, prefix="/api/v1", tags=["documents"])
app.include_router(models.router, prefix="/api/v1/models", tags=["models"])

@app.on_event("startup")
async def startup_event():
    """Initialize database and services on startup"""
    await init_db()

@app.get("/")
async def root():
    return {"message": "ASCAES Backend API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "ASCAES Backend is running - UPDATED"}

@app.get("/api/v1/models/status")
async def models_status_direct():
    """Direct models status endpoint for testing"""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/version", timeout=5.0)
        response.raise_for_status()
        data = response.json()

        return {
            "connected": True,
            "status": "connected",
            "version": data.get("version", "unknown"),
            "ollama_url": "http://localhost:11434",
            "message": "Ollama service is available"
        }

    except requests.RequestException:
        return {
            "connected": False,
            "status": "disconnected",
            "ollama_url": "http://localhost:11434",
            "error": "Ollama service is not available. Please ensure Ollama is running on localhost:11434"
        }
    except Exception as e:
        return {
            "connected": False,
            "status": "error",
            "ollama_url": "http://localhost:11434",
            "error": f"Error connecting to Ollama: {str(e)}"
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
