"""
Generation Service - Core document generation logic
Orchestrates the RAG-powered document creation process
"""

import httpx
import asyncio
import json
import requests
from typing import Dict, Any, List
from sqlalchemy.orm import Session
from app.db.models import Document, Chapter, AcademicFormattingSettings
# from app.core.rag_service import RAGService
from app.core.protocols import (
    WRITING_STYLE_DEFINITIONS, 
    CULTURAL_INFLECTION_DEFINITIONS,
    HUMANIZATION_PROTOCOL,
    GRAMMATICAL_CORRECTION_PROTOCOL,
    get_academic_formatting_protocol,
    get_word_count_estimate
)

class GenerationService:
    def __init__(self):
        self.ollama_base_url = "http://localhost:11434"
        self.generation_model = "deepseek-r1:1.5b"  # Primary model for academic generation
        self.fallback_model = "gemma3:1b"  # Fallback model for reliability
        # self.rag_service = RAGService()  # Temporarily disabled
    
    async def call_ollama_generate(self, prompt: str, model: str = None) -> str:
        """Enhanced Ollama API call with better error handling"""
        if model is None:
            model = self.generation_model

        try:
            # Increased token limit and adjusted parameters
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json={
                    "model": model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,      # Optimal for academic content
                        "top_p": 0.9,           # Balanced creativity
                        "top_k": 40,            # Focused token selection
                        "num_predict": 800,     # Complete paragraphs
                        "repeat_penalty": 1.1,  # Prevent repetition
                        "seed": -1,
                        "num_ctx": 4096,        # Large context window
                        "keep_alive": "10m"     # Keep model loaded for 10 minutes
                    }
                },
                timeout=300.0  # 5 minute timeout for stability
            )

            response.raise_for_status()
            result = response.json()
            content = result.get("response", "")

            if not content or len(content.strip()) < 50:
                return "Unable to generate sufficient content. Please check Ollama model availability."

            # Enhanced content filtering
            content = self.filter_reasoning_content(content)

            # Additional validation
            if len(content.strip()) < 100:
                return f"Generated content too short. Raw content: {content[:200]}..."

            return content

        except requests.exceptions.Timeout:
            return "Generation timed out. Please try again."
        except requests.exceptions.ConnectionError:
            return "Cannot connect to Ollama. Please ensure it's running."
        except Exception as e:
            print(f"Error calling Ollama: {e}")
            return f"Error generating content: {str(e)}"

    async def call_ollama_generate_with_fallback(self, prompt: str) -> str:
        """Try primary model with retry logic and fallback"""

        max_retries = 3

        for attempt in range(max_retries):
            try:
                print(f"Generation attempt {attempt + 1}/{max_retries}")
                content = await self.call_ollama_generate(prompt, self.generation_model)

                # Validate content quality
                if content and len(content.strip()) >= 200:  # Minimum content length
                    word_count = len(content.split())
                    if word_count >= 50:  # Minimum word count
                        print(f"✅ Generated {word_count} words successfully")
                        return content
                    else:
                        print(f"⚠️ Content too short ({word_count} words), retrying...")
                else:
                    print(f"⚠️ Content insufficient (length: {len(content) if content else 0}), retrying...")

                # If content is insufficient, try again with modified prompt
                if attempt < max_retries - 1:
                    prompt += f"\n\nIMPORTANT: Generate at least 300 words with complete sentences and paragraphs."

            except Exception as e:
                print(f"Attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    # Last attempt - try fallback model
                    try:
                        print("Trying fallback model...")
                        return await self.call_ollama_generate(prompt, self.fallback_model)
                    except Exception as fallback_error:
                        print(f"Fallback model also failed: {fallback_error}")
                        return f"Unable to generate content after {max_retries} attempts. Please try again."

        return "Generation failed after all retry attempts."

    def filter_reasoning_content(self, content: str) -> str:
        """Enhanced filter for DeepSeek reasoning/thinking process"""
        import re

        # First, look for the pattern where thinking ends and actual content begins
        # DeepSeek often uses "...done thinking." or similar markers
        thinking_end_patterns = [
            r'\.\.\.done thinking\.',
            r'\.\.\.thinking complete\.',
            r'\.\.\.end of thinking\.',
            r'Now, let me write the actual content:',
            r'Here\'s the actual content:',
            r'The actual content is:'
        ]

        # Try to find where thinking ends and content begins
        for pattern in thinking_end_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                # Take everything after the thinking marker
                content = content[match.end():].strip()
                break

        # Remove explicit thinking tags
        thinking_patterns = [
            r'<think>.*?</think>',
            r'<thinking>.*?</thinking>',
            r'<reason>.*?</reason>',
            r'<reasoning>.*?</reasoning>',
            r'Thinking\.\.\..*?\.\.\.done thinking\.',
        ]

        for pattern in thinking_patterns:
            content = re.sub(pattern, '', content, flags=re.DOTALL | re.IGNORECASE)

        # Remove obvious reasoning sections at the beginning
        lines = content.split('\n')
        filtered_lines = []
        skip_reasoning = True

        for line in lines:
            line = line.strip()

            # Skip empty lines at the beginning
            if not line and skip_reasoning:
                continue

            # Check if this line looks like reasoning
            reasoning_indicators = [
                r'^(Okay|Alright|Well|So|Now|Let me|I need to|I should|I will|I\'ll)',
                r'^(The user|The task|The document|The chapter)',
                r'^(Looking at|Considering|Given that|Based on)',
                r'^(Hmm|Wait|But|Maybe|Perhaps)',
                r'^\d+\.\s*(First|Second|Third|Fourth|Fifth)',
                r'^(Thinking|Analysis|Reasoning):',
            ]

            is_reasoning = False
            for pattern in reasoning_indicators:
                if re.match(pattern, line, re.IGNORECASE):
                    is_reasoning = True
                    break

            # If we hit actual content (not reasoning), start including lines
            if not is_reasoning and line:
                skip_reasoning = False

            # Include the line if we're past the reasoning section
            if not skip_reasoning:
                filtered_lines.append(line)
            elif not is_reasoning and line:
                # This might be actual content, include it
                filtered_lines.append(line)
                skip_reasoning = False

        # Join back and clean up
        result = '\n'.join(filtered_lines)

        # Remove multiple consecutive newlines
        result = re.sub(r'\n\s*\n\s*\n+', '\n\n', result)

        # Remove any remaining thinking indicators at the start
        result = re.sub(r'^(Thinking|Analysis|Reasoning):\s*', '', result, flags=re.IGNORECASE)

        return result.strip()
    
    def construct_prompt(self, document: Document, chapter_title: str, chapter_number: int, 
                        relevant_context: List[Dict[str, Any]] = None) -> str:
        """Construct the complete prompt for chapter generation"""
        
        # Get writing style and cultural inflection
        style_def = WRITING_STYLE_DEFINITIONS.get(document.writing_style, {})

        # Handle cultural inflection (can be string or dict)
        cultural_text = ""
        if isinstance(document.cultural_inflection, dict):
            # New format: {"American": 70, "German": 30}
            cultures = []
            for culture, percentage in document.cultural_inflection.items():
                if percentage > 0:
                    cultures.append(f"{culture} ({percentage}%)")
            cultural_text = " + ".join(cultures) if cultures else "American"
        else:
            # Old format: "American"
            cultural_text = document.cultural_inflection
        
        # Build context section (simplified)
        context_section = ""
        if relevant_context:
            context_texts = [chunk["text"][:300] for chunk in relevant_context]  # Limit context length
            context_section = f"""
PREVIOUS CONTENT CONTEXT:
{chr(10).join([f"- {text}" for text in context_texts])}
"""
        
        # Academic formatting
        academic_settings = AcademicFormattingSettings(
            enable_academic_formatting=document.enable_academic_formatting,
            citation_style=document.citation_style,
            student_name=document.student_name,
            course_name=document.course_name,
            instructor_name=document.instructor_name,
            due_date=document.due_date
        )
        academic_formatting = get_academic_formatting_protocol(academic_settings)
        
        # Humanization protocol
        humanization = HUMANIZATION_PROTOCOL if document.improve_realism else ""
        
        # Enhanced prompt for better continuity and completion
        target_words = max(400, document.estimated_words // 10)  # Increased minimum

        prompt = f"""You are writing Chapter {chapter_number}: "{chapter_title}" for the {document.document_type.lower()} titled "{document.title}".

{context_section}

CHAPTER REQUIREMENTS:
- Write exactly {target_words} words (minimum {target_words-50}, maximum {target_words+50})
- Use {document.writing_style} writing style with {cultural_text} cultural perspective
- Create 3-4 complete paragraphs with smooth transitions
- Each paragraph should be 100-150 words
- Include specific examples, detailed explanations, and thorough analysis
- Ensure the chapter flows logically from introduction to conclusion
- End with a complete sentence that provides closure to the chapter topic

WRITING GUIDELINES:
- Start with an engaging opening that introduces the chapter topic
- Develop ideas progressively through well-structured paragraphs
- Use clear topic sentences and supporting details
- Conclude with insights that connect to the overall document theme
- Maintain academic tone while being accessible and engaging

Write the complete chapter content now. Begin immediately with the first paragraph:"""
        
        return prompt
    
    async def generate_chapter_outline(self, document: Document) -> List[str]:
        """Generate chapter titles/outline for the document"""
        
        style_def = WRITING_STYLE_DEFINITIONS.get(document.writing_style, {})
        
        outline_prompt = f"""Create a comprehensive 10-chapter outline for a {document.document_type.lower()} titled "{document.title}".

DOCUMENT SPECIFICATIONS:
- Type: {document.document_type}
- Pages: {document.pages}
- Words: {document.estimated_words}
- Style: {document.writing_style}

OUTLINE REQUIREMENTS:
- Generate exactly 10 chapter titles
- Each chapter should cover 400-500 words
- Ensure logical progression from introduction to conclusion
- Make titles specific and descriptive
- Cover all major aspects of the topic comprehensively

STRUCTURE GUIDELINES:
- Chapter 1: Introduction and overview
- Chapters 2-3: Foundational concepts
- Chapters 4-7: Core content and analysis
- Chapters 8-9: Applications and implications
- Chapter 10: Conclusion and future directions

Return only the chapter titles, one per line, no numbering:"""
        
        response = await self.call_ollama_generate_with_fallback(outline_prompt)
        
        # Parse chapter titles from response
        lines = [line.strip() for line in response.split('\n') if line.strip()]
        chapter_titles = []
        
        for line in lines:
            # Remove any numbering or formatting
            clean_title = line.strip()
            if clean_title.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '10.')):
                clean_title = clean_title.split('.', 1)[1].strip()
            elif clean_title.startswith(('Chapter', 'chapter')):
                parts = clean_title.split(':', 1)
                if len(parts) > 1:
                    clean_title = parts[1].strip()
            
            if clean_title and len(clean_title) > 5:  # Ensure meaningful titles
                chapter_titles.append(clean_title)
        
        # Ensure we have exactly 10 chapters
        if len(chapter_titles) < 10:
            # Add generic chapters if needed
            for i in range(len(chapter_titles), 10):
                chapter_titles.append(f"Chapter {i+1} Content")
        elif len(chapter_titles) > 10:
            chapter_titles = chapter_titles[:10]
        
        return chapter_titles
    
    async def run_generation_flow(self, document_id: int, db: Session):
        """Main generation flow - orchestrates the entire document creation process"""
        
        # Get document from database
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            return
        
        try:
            # Update status to generating
            document.status = "generating"
            document.progress = 0.0
            db.commit()
            
            # Clear any existing memory for this document
            self.rag_service.clear_document_memory(document_id)
            
            # Generate chapter outline
            chapter_titles = await self.generate_chapter_outline(document)
            
            # Create chapter records in database
            for i, title in enumerate(chapter_titles):
                chapter = Chapter(
                    document_id=document_id,
                    chapter_number=i + 1,
                    title=title,
                    status="pending"
                )
                db.add(chapter)
            db.commit()
            
            # Generate each chapter
            chapters = db.query(Chapter).filter(Chapter.document_id == document_id).order_by(Chapter.chapter_number).all()
            
            for i, chapter in enumerate(chapters):
                try:
                    # Update chapter status
                    chapter.status = "generating"
                    db.commit()
                    
                    # Get relevant context from previous chapters
                    relevant_context = await self.rag_service.retrieve_relevant_context(
                        chapter.title, document_id, n_results=3
                    )
                    
                    # Generate chapter content with fallback
                    prompt = self.construct_prompt(document, chapter.title, chapter.chapter_number, relevant_context)
                    content = await self.call_ollama_generate_with_fallback(prompt)
                    
                    # Update chapter with generated content
                    chapter.content = content
                    chapter.word_count = len(content.split())
                    chapter.status = "completed"
                    db.commit()
                    
                    # Add chapter content to RAG memory
                    await self.rag_service.add_text_to_memory(
                        content, document_id, chapter.id, chapter.title
                    )
                    
                    # Update document progress
                    document.progress = ((i + 1) / len(chapters)) * 100
                    db.commit()
                    
                    # 6-second delay between chapters as specified
                    await asyncio.sleep(6)
                    
                except Exception as e:
                    print(f"Error generating chapter {chapter.chapter_number}: {e}")
                    chapter.status = "error"
                    db.commit()
            
            # Mark document as completed
            document.status = "completed"
            document.progress = 100.0
            db.commit()
            
        except Exception as e:
            print(f"Error in generation flow: {e}")
            document.status = "error"
            db.commit()
    
    async def check_grammar(self, text: str) -> str:
        """Check and correct grammar using Ollama"""
        if not text or len(text.strip()) == 0:
            return text
        
        prompt = f"{GRAMMATICAL_CORRECTION_PROTOCOL}\n\nText to correct:\n{text}"
        
        try:
            corrected = await self.call_ollama_generate(prompt)
            return corrected.strip()
        except Exception as e:
            print(f"Error checking grammar: {e}")
            return text  # Return original text if correction fails
