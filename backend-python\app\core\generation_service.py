"""
Generation Service - Core document generation logic
Orchestrates the RAG-powered document creation process
"""

import httpx
import asyncio
import json
from typing import Dict, Any, List
from sqlalchemy.orm import Session
from app.db.models import Document, Chapter, AcademicFormattingSettings
from app.core.rag_service import RAGService
from app.core.protocols import (
    WRITING_STYLE_DEFINITIONS, 
    CULTURAL_INFLECTION_DEFINITIONS,
    HUMANIZATION_PROTOCOL,
    GRAMMATICAL_CORRECTION_PROTOCOL,
    get_academic_formatting_protocol,
    get_word_count_estimate
)

class GenerationService:
    def __init__(self):
        self.ollama_base_url = "http://localhost:11434"
        self.generation_model = "deepseek-r1:1.5b"  # Using the installed model
        self.rag_service = RAGService()
    
    async def call_ollama_generate(self, prompt: str, model: str = None) -> str:
        """Make API call to Ollama for text generation using the working generate endpoint"""
        if model is None:
            model = self.generation_model

        try:
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.ollama_base_url}/api/generate",
                    json={
                        "model": model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.7,
                            "top_p": 0.9,
                            "top_k": 40,
                            "num_predict": 800  # Allow longer responses for chapters
                        }
                    }
                )
                response.raise_for_status()
                result = response.json()
                content = result.get("response", "")

                if not content:
                    return "Unable to generate content. Please check Ollama model availability."

                # Filter out DeepSeek reasoning/thinking process
                content = self.filter_reasoning_content(content)

                return content

        except Exception as e:
            print(f"Error calling Ollama: {e}")
            return f"Error generating content: {str(e)}"

    def filter_reasoning_content(self, content: str) -> str:
        """Filter out DeepSeek reasoning/thinking process and return only the actual content"""
        import re

        # Remove thinking tags and content between them
        content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
        content = re.sub(r'<thinking>.*?</thinking>', '', content, flags=re.DOTALL)

        # Remove lines that look like reasoning
        lines = content.split('\n')
        filtered_lines = []

        skip_patterns = [
            r'^(Alright|Okay|Let me|I need to|I should|I\'ll|First)',
            r'^(The user|The task|The document|The chapter)',
            r'^(Looking at|Considering|Given that|Based on)',
            r'^(So |Now |Next |Then )',
            r'^\d+\.\s*(First|Second|Third|Fourth|Fifth|Sixth|Seventh|Eighth|Ninth|Tenth)',
        ]

        for line in lines:
            line = line.strip()
            if not line:
                filtered_lines.append(line)
                continue

            # Check if line matches reasoning patterns
            is_reasoning = False
            for pattern in skip_patterns:
                if re.match(pattern, line, re.IGNORECASE):
                    is_reasoning = True
                    break

            # Skip obvious reasoning lines
            if not is_reasoning:
                filtered_lines.append(line)

        # Join back and clean up
        result = '\n'.join(filtered_lines)

        # Remove multiple consecutive newlines
        result = re.sub(r'\n\s*\n\s*\n+', '\n\n', result)

        return result.strip()
    
    def construct_prompt(self, document: Document, chapter_title: str, chapter_number: int, 
                        relevant_context: List[Dict[str, Any]] = None) -> str:
        """Construct the complete prompt for chapter generation"""
        
        # Get writing style and cultural inflection
        style_def = WRITING_STYLE_DEFINITIONS.get(document.writing_style, {})
        culture_def = CULTURAL_INFLECTION_DEFINITIONS.get(document.cultural_inflection, {})
        
        # Build context section
        context_section = ""
        if relevant_context:
            context_texts = [chunk["text"] for chunk in relevant_context]
            context_section = f"""
### DOCUMENT CONTEXT ###
Here are relevant excerpts from previously written chapters of this document:

{chr(10).join([f"Context {i+1}: {text}" for i, text in enumerate(context_texts)])}

Use this context to maintain consistency and coherence with the rest of the document.
"""
        
        # Academic formatting
        academic_settings = AcademicFormattingSettings(
            enable_academic_formatting=document.enable_academic_formatting,
            citation_style=document.citation_style,
            student_name=document.student_name,
            course_name=document.course_name,
            instructor_name=document.instructor_name,
            due_date=document.due_date
        )
        academic_formatting = get_academic_formatting_protocol(academic_settings)
        
        # Humanization protocol
        humanization = HUMANIZATION_PROTOCOL if document.improve_realism else ""
        
        # Construct the complete prompt
        prompt = f"""
### DOCUMENT GENERATION TASK ###

You are writing Chapter {chapter_number} titled "{chapter_title}" for a {document.document_type.lower()} titled "{document.title}".

### WRITING STYLE: {document.writing_style} ###
{style_def.get('DESCRIPTION', '')}

### CULTURAL INFLECTION: {document.cultural_inflection} ###
Rhetorical Style: {culture_def.get('RHETORICAL_STYLE', '')}
Tone: {culture_def.get('TONE', '')}
Key Values: {culture_def.get('KEY_VALUES_EMPHASIZED', '')}

{context_section}

{academic_formatting}

{humanization}

### GENERATION REQUIREMENTS ###
- Write a complete chapter of approximately {max(250, document.estimated_words // 10)} words (minimum 250 words)
- Maintain consistency with the document title and type
- Use the specified writing style and cultural inflection
- Create engaging, well-structured content with multiple paragraphs
- Include detailed explanations, examples, and thorough coverage of the topic
- Do not include chapter numbers or titles in your response - only the content
- Do not include any reasoning, thinking process, or meta-commentary

### CHAPTER TITLE ###
{chapter_title}

### IMPORTANT ###
- Start writing the chapter content immediately
- Do not include any thinking, reasoning, or planning
- Do not include meta-commentary about the task
- Write only the actual chapter content
- Begin with the first paragraph of the chapter

Generate the chapter content now:
"""
        
        return prompt
    
    async def generate_chapter_outline(self, document: Document) -> List[str]:
        """Generate chapter titles/outline for the document"""
        
        style_def = WRITING_STYLE_DEFINITIONS.get(document.writing_style, {})
        
        outline_prompt = f"""
Generate a detailed chapter outline for a {document.document_type.lower()} titled "{document.title}".

Writing Style: {document.writing_style}
Style Description: {style_def.get('DESCRIPTION', '')}

Document Type: {document.document_type}
Target Pages: {document.pages}
Estimated Words: {document.estimated_words}

Create exactly 10 chapter titles that would be appropriate for this document. Each chapter should be substantial and contribute meaningfully to the overall work.

Return only the chapter titles, one per line, without numbers or additional formatting:
"""
        
        response = await self.call_ollama_generate(outline_prompt)
        
        # Parse chapter titles from response
        lines = [line.strip() for line in response.split('\n') if line.strip()]
        chapter_titles = []
        
        for line in lines:
            # Remove any numbering or formatting
            clean_title = line.strip()
            if clean_title.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '10.')):
                clean_title = clean_title.split('.', 1)[1].strip()
            elif clean_title.startswith(('Chapter', 'chapter')):
                parts = clean_title.split(':', 1)
                if len(parts) > 1:
                    clean_title = parts[1].strip()
            
            if clean_title and len(clean_title) > 5:  # Ensure meaningful titles
                chapter_titles.append(clean_title)
        
        # Ensure we have exactly 10 chapters
        if len(chapter_titles) < 10:
            # Add generic chapters if needed
            for i in range(len(chapter_titles), 10):
                chapter_titles.append(f"Chapter {i+1} Content")
        elif len(chapter_titles) > 10:
            chapter_titles = chapter_titles[:10]
        
        return chapter_titles
    
    async def run_generation_flow(self, document_id: int, db: Session):
        """Main generation flow - orchestrates the entire document creation process"""
        
        # Get document from database
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            return
        
        try:
            # Update status to generating
            document.status = "generating"
            document.progress = 0.0
            db.commit()
            
            # Clear any existing memory for this document
            self.rag_service.clear_document_memory(document_id)
            
            # Generate chapter outline
            chapter_titles = await self.generate_chapter_outline(document)
            
            # Create chapter records in database
            for i, title in enumerate(chapter_titles):
                chapter = Chapter(
                    document_id=document_id,
                    chapter_number=i + 1,
                    title=title,
                    status="pending"
                )
                db.add(chapter)
            db.commit()
            
            # Generate each chapter
            chapters = db.query(Chapter).filter(Chapter.document_id == document_id).order_by(Chapter.chapter_number).all()
            
            for i, chapter in enumerate(chapters):
                try:
                    # Update chapter status
                    chapter.status = "generating"
                    db.commit()
                    
                    # Get relevant context from previous chapters
                    relevant_context = await self.rag_service.retrieve_relevant_context(
                        chapter.title, document_id, n_results=3
                    )
                    
                    # Generate chapter content
                    prompt = self.construct_prompt(document, chapter.title, chapter.chapter_number, relevant_context)
                    content = await self.call_ollama_generate(prompt)
                    
                    # Update chapter with generated content
                    chapter.content = content
                    chapter.word_count = len(content.split())
                    chapter.status = "completed"
                    db.commit()
                    
                    # Add chapter content to RAG memory
                    await self.rag_service.add_text_to_memory(
                        content, document_id, chapter.id, chapter.title
                    )
                    
                    # Update document progress
                    document.progress = ((i + 1) / len(chapters)) * 100
                    db.commit()
                    
                    # 6-second delay between chapters as specified
                    await asyncio.sleep(6)
                    
                except Exception as e:
                    print(f"Error generating chapter {chapter.chapter_number}: {e}")
                    chapter.status = "error"
                    db.commit()
            
            # Mark document as completed
            document.status = "completed"
            document.progress = 100.0
            db.commit()
            
        except Exception as e:
            print(f"Error in generation flow: {e}")
            document.status = "error"
            db.commit()
    
    async def check_grammar(self, text: str) -> str:
        """Check and correct grammar using Ollama"""
        if not text or len(text.strip()) == 0:
            return text
        
        prompt = f"{GRAMMATICAL_CORRECTION_PROTOCOL}\n\nText to correct:\n{text}"
        
        try:
            corrected = await self.call_ollama_generate(prompt)
            return corrected.strip()
        except Exception as e:
            print(f"Error checking grammar: {e}")
            return text  # Return original text if correction fails
