"""
Generation Service - Core document generation logic
Orchestrates the RAG-powered document creation process
"""

import httpx
import asyncio
import json
import requests
from typing import Dict, Any, List
from sqlalchemy.orm import Session
from app.db.models import Document, Chapter, AcademicFormattingSettings
# from app.core.rag_service import RAGService
from app.core.protocols import (
    WRITING_STYLE_DEFINITIONS, 
    CULTURAL_INFLECTION_DEFINITIONS,
    HUMANIZATION_PROTOCOL,
    GRAMMATICAL_CORRECTION_PROTOCOL,
    get_academic_formatting_protocol,
    get_word_count_estimate
)

class GenerationService:
    def __init__(self):
        self.ollama_base_url = "http://localhost:11434"
        self.generation_model = "deepseek-r1:1.5b"  # Primary model for academic generation
        self.fallback_model = "gemma3:1b"  # Fallback model for reliability
        # self.rag_service = RAGService()  # Temporarily disabled
    
    async def call_ollama_generate(self, prompt: str, model: str = None) -> str:
        """Enhanced Ollama API call with better error handling"""
        if model is None:
            model = self.generation_model

        try:
            # Increased token limit and adjusted parameters
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json={
                    "model": model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,      # Optimal for academic content
                        "top_p": 0.9,           # Balanced creativity
                        "top_k": 40,            # Focused token selection
                        "num_predict": 800,     # Complete paragraphs
                        "repeat_penalty": 1.1,  # Prevent repetition
                        "seed": -1,
                        "num_ctx": 4096,        # Large context window
                        "keep_alive": "10m"     # Keep model loaded for 10 minutes
                    }
                },
                timeout=180.0  # 3 minute timeout for proper generation
            )

            response.raise_for_status()
            result = response.json()
            content = result.get("response", "")

            if not content or len(content.strip()) < 50:
                return "Unable to generate sufficient content. Please check Ollama model availability."

            # Enhanced content filtering
            content = self.filter_reasoning_content(content)

            # Additional validation
            if len(content.strip()) < 100:
                return f"Generated content too short. Raw content: {content[:200]}..."

            return content

        except requests.exceptions.Timeout:
            print(f"⚠️ Ollama timeout, using fallback content")
            return self.generate_fallback_content(prompt)
        except requests.exceptions.ConnectionError:
            print(f"⚠️ Ollama connection error, using fallback content")
            return self.generate_fallback_content(prompt)
        except Exception as e:
            print(f"Error calling Ollama: {e}")
            return self.generate_fallback_content(prompt)

    async def call_ollama_generate_with_fallback(self, prompt: str) -> str:
        """Try primary model with retry logic and fallback"""

        max_retries = 3

        for attempt in range(max_retries):
            try:
                print(f"Generation attempt {attempt + 1}/{max_retries}")
                content = await self.call_ollama_generate(prompt, self.generation_model)

                # Validate content quality
                if content and len(content.strip()) >= 200:  # Minimum content length
                    word_count = len(content.split())
                    if word_count >= 50:  # Minimum word count
                        print(f"✅ Generated {word_count} words successfully")
                        return content
                    else:
                        print(f"⚠️ Content too short ({word_count} words), retrying...")
                else:
                    print(f"⚠️ Content insufficient (length: {len(content) if content else 0}), retrying...")

                # If content is insufficient, try again with modified prompt
                if attempt < max_retries - 1:
                    prompt += f"\n\nIMPORTANT: Generate at least 300 words with complete sentences and paragraphs."

            except Exception as e:
                print(f"Attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    # Last attempt - try fallback model
                    try:
                        print("Trying fallback model...")
                        return await self.call_ollama_generate(prompt, self.fallback_model)
                    except Exception as fallback_error:
                        print(f"Fallback model also failed: {fallback_error}")
                        return f"Unable to generate content after {max_retries} attempts. Please try again."

        return "Generation failed after all retry attempts."

    def generate_fallback_content(self, prompt: str) -> str:
        """Generate fallback content when Ollama is unavailable or slow"""

        # Extract topic from prompt
        topic = "academic content"
        if "Chapter" in prompt:
            # Extract chapter title
            lines = prompt.split('\n')
            for line in lines:
                if 'Chapter' in line and ':' in line:
                    topic = line.split(':', 1)[1].strip().strip('"')
                    break

        # Generate structured academic content
        fallback_content = f"""
This chapter explores the fundamental concepts and implications of {topic} within the broader academic context. The significance of this topic cannot be overstated in contemporary scholarly discourse.

The theoretical framework surrounding {topic} has evolved considerably over recent decades. Researchers have identified several key dimensions that warrant careful examination. These include the methodological approaches, empirical findings, and theoretical contributions that have shaped our understanding.

From a methodological perspective, the study of {topic} requires a multidisciplinary approach. Various research paradigms have been employed to investigate different aspects of this phenomenon. Quantitative methods provide statistical insights, while qualitative approaches offer deeper contextual understanding.

The empirical evidence suggests that {topic} plays a crucial role in shaping outcomes across multiple domains. Several studies have demonstrated significant correlations between key variables, though causation remains a subject of ongoing debate among scholars.

Contemporary applications of {topic} extend beyond traditional academic boundaries. Practitioners in various fields have begun to recognize the practical implications of theoretical insights. This has led to innovative approaches and novel solutions to longstanding challenges.

Future research directions should focus on addressing current limitations and exploring emerging opportunities. The integration of new technologies and methodologies promises to advance our understanding significantly. Collaborative efforts across disciplines will be essential for continued progress.

In conclusion, {topic} represents a vital area of inquiry that continues to evolve. The synthesis of theoretical insights and practical applications offers promising avenues for future development. Continued scholarly attention to this area will undoubtedly yield valuable contributions to the field.
        """.strip()

        return fallback_content

    def filter_reasoning_content(self, content: str) -> str:
        """Enhanced filter for DeepSeek reasoning/thinking process and markdown cleanup"""
        import re

        # Remove all thinking/reasoning tags first (most aggressive approach)
        thinking_patterns = [
            r'<think>.*?</think>',
            r'<thinking>.*?</thinking>',
            r'<reason>.*?</reason>',
            r'<reasoning>.*?</reasoning>',
            r'\\u003cthink\\u003e.*?\\u003c/think\\u003e',  # Handle escaped unicode
            r'&lt;think&gt;.*?&lt;/think&gt;',  # HTML encoded
        ]

        for pattern in thinking_patterns:
            content = re.sub(pattern, '', content, flags=re.DOTALL | re.IGNORECASE)

        # Look for thinking end markers and take content after them
        thinking_end_patterns = [
            r'\.\.\.done thinking\.',
            r'\.\.\.thinking complete\.',
            r'\.\.\.end of thinking\.',
            r'Now, let me write the actual content:',
            r'Here\'s the actual content:',
            r'The actual content is:',
            r'Okay, so I need to write.*?(?=\n\n|\n[A-Z])',
            r'First, the chapter needs to be.*?(?=\n\n|\n[A-Z])',
        ]

        # Try to find where thinking ends and content begins
        for pattern in thinking_end_patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
            if match:
                # Take everything after the thinking marker
                content = content[match.end():].strip()
                break

        # Remove markdown formatting for clean text
        # Remove headers (# ## ###)
        content = re.sub(r'^#{1,6}\s+', '', content, flags=re.MULTILINE)

        # Remove bold/italic markers
        content = re.sub(r'\*\*([^*]+)\*\*', r'\1', content)  # **bold**
        content = re.sub(r'\*([^*]+)\*', r'\1', content)      # *italic*
        content = re.sub(r'__([^_]+)__', r'\1', content)      # __bold__
        content = re.sub(r'_([^_]+)_', r'\1', content)        # _italic_

        # Remove other markdown elements
        content = re.sub(r'`([^`]+)`', r'\1', content)        # `code`
        content = re.sub(r'```[^`]*```', '', content, flags=re.DOTALL)  # code blocks

        # Remove obvious reasoning sections at the beginning
        lines = content.split('\n')
        filtered_lines = []
        skip_reasoning = True

        for line in lines:
            line = line.strip()

            # Skip empty lines at the beginning
            if not line and skip_reasoning:
                continue

            # Check if this line looks like reasoning
            reasoning_indicators = [
                r'^(Okay|Alright|Well|So|Now|Let me|I need to|I should|I will|I\'ll)',
                r'^(The user|The task|The document|The chapter)',
                r'^(Looking at|Considering|Given that|Based on)',
                r'^(Hmm|Wait|But|Maybe|Perhaps)',
                r'^\d+\.\s*(First|Second|Third|Fourth|Fifth)',
                r'^(Thinking|Analysis|Reasoning):',
            ]

            is_reasoning = False
            for pattern in reasoning_indicators:
                if re.match(pattern, line, re.IGNORECASE):
                    is_reasoning = True
                    break

            # If we hit actual content (not reasoning), start including lines
            if not is_reasoning and line:
                skip_reasoning = False

            # Include the line if we're past the reasoning section
            if not skip_reasoning:
                filtered_lines.append(line)
            elif not is_reasoning and line:
                # This might be actual content, include it
                filtered_lines.append(line)
                skip_reasoning = False

        # Join back and clean up
        result = '\n'.join(filtered_lines)

        # Remove multiple consecutive newlines
        result = re.sub(r'\n\s*\n\s*\n+', '\n\n', result)

        # Remove any remaining thinking indicators at the start
        result = re.sub(r'^(Thinking|Analysis|Reasoning):\s*', '', result, flags=re.IGNORECASE)

        return result.strip()
    
    def construct_prompt(self, document: Document, chapter_title: str, chapter_number: int, 
                        relevant_context: List[Dict[str, Any]] = None) -> str:
        """Construct the complete prompt for chapter generation"""
        
        # Get writing style and cultural inflection
        style_def = WRITING_STYLE_DEFINITIONS.get(document.writing_style, {})

        # Handle cultural inflection (can be string or dict)
        cultural_text = ""
        if isinstance(document.cultural_inflection, dict):
            # New format: {"American": 70, "German": 30}
            cultures = []
            for culture, percentage in document.cultural_inflection.items():
                if percentage > 0:
                    cultures.append(f"{culture} ({percentage}%)")
            cultural_text = " + ".join(cultures) if cultures else "American"
        else:
            # Old format: "American"
            cultural_text = document.cultural_inflection
        
        # Build context section (simplified)
        context_section = ""
        if relevant_context:
            context_texts = [chunk["text"][:300] for chunk in relevant_context]  # Limit context length
            context_section = f"""
PREVIOUS CONTENT CONTEXT:
{chr(10).join([f"- {text}" for text in context_texts])}
"""
        
        # Academic formatting
        academic_settings = AcademicFormattingSettings(
            enable_academic_formatting=document.enable_academic_formatting,
            citation_style=document.citation_style,
            student_name=document.student_name,
            course_name=document.course_name,
            instructor_name=document.instructor_name,
            due_date=document.due_date
        )
        academic_formatting = get_academic_formatting_protocol(academic_settings)
        
        # Humanization protocol
        humanization = HUMANIZATION_PROTOCOL if document.improve_realism else ""
        
        # Enhanced prompt for better continuity and completion
        target_words = max(400, document.estimated_words // 10)  # Increased minimum

        prompt = f"""You are writing Chapter {chapter_number}: "{chapter_title}" for the {document.document_type.lower()} titled "{document.title}".

{context_section}

CHAPTER REQUIREMENTS:
- Write exactly {target_words} words (minimum {target_words-50}, maximum {target_words+50})
- Use {document.writing_style} writing style with {cultural_text} cultural perspective
- Create 3-4 complete paragraphs with smooth transitions
- Each paragraph should be 100-150 words
- Include specific examples, detailed explanations, and thorough analysis
- Ensure the chapter flows logically from introduction to conclusion
- End with a complete sentence that provides closure to the chapter topic

WRITING GUIDELINES:
- Start with an engaging opening that introduces the chapter topic
- Develop ideas progressively through well-structured paragraphs
- Use clear topic sentences and supporting details
- Conclude with insights that connect to the overall document theme
- Maintain academic tone while being accessible and engaging

Write the complete chapter content now. Begin immediately with the first paragraph:"""
        
        return prompt

    def construct_enhanced_prompt(self, document: Document, chapter_title: str, chapter_number: int,
                                target_words: int, previous_content: str = None, relevant_context: str = None) -> str:
        """Construct enhanced prompt with word count control and flow continuity"""

        style_def = WRITING_STYLE_DEFINITIONS.get(document.writing_style, {})

        # Flow continuity instruction
        flow_instruction = ""
        if previous_content and chapter_number > 1:
            flow_instruction = f"""
CONTENT FLOW CONTINUITY:
The previous chapter ended with: "...{previous_content}"

Ensure your chapter flows naturally from this content. If the previous chapter ended mid-sentence or mid-thought, continue seamlessly. Reference key points from the previous section where appropriate to maintain document coherence.
"""

        prompt = f"""You are writing Chapter {chapter_number} of a {document.document_type.lower()} titled "{document.title}".

CHAPTER SPECIFICATIONS:
- Chapter Title: {chapter_title}
- Target Word Count: {target_words} words (STRICTLY ENFORCE - must be between {target_words-10} and {target_words+10} words)
- Writing Style: {document.writing_style}
- Document Type: {document.document_type}

{style_def.get('description', '')}

{flow_instruction}

CRITICAL REQUIREMENTS:
1. Write EXACTLY {target_words} words (±10 words tolerance)
2. Maintain academic rigor and proper structure
3. Ensure complete sentences and thoughts - NO ABRUPT ENDINGS
4. Use clear, professional language without markdown formatting
5. Create smooth transitions between paragraphs
6. End with a complete thought that flows to the next chapter

CONTENT STRUCTURE:
- Opening paragraph: Introduce the chapter topic and connect to overall document theme
- Body paragraphs: Develop key concepts with examples and analysis
- Closing paragraph: Summarize key points and transition to next chapter

FORMATTING RULES:
- NO markdown symbols (*, #, **, etc.)
- NO thinking tags or meta-commentary
- Plain text only with proper paragraph breaks
- Academic tone throughout

{f"RELEVANT CONTEXT: {relevant_context}" if relevant_context else ""}

Write the complete chapter content now. Begin immediately with the first paragraph and ensure you reach exactly {target_words} words:"""

        return prompt

    async def generate_document_outline(self, document: Document) -> List[dict]:
        """Generate smart outline with proper page distribution"""
        import re

        total_pages = document.pages
        words_per_page = 255  # Target 250-260 words per page
        total_words = total_pages * words_per_page

        # Determine optimal number of chapters based on document length
        if total_pages <= 10:
            num_chapters = max(3, total_pages // 3)  # 3-4 chapters for short docs
        elif total_pages <= 50:
            num_chapters = max(5, total_pages // 8)  # 5-7 chapters for medium docs
        else:
            num_chapters = max(8, min(12, total_pages // 10))  # 8-12 chapters for long docs

        style_def = WRITING_STYLE_DEFINITIONS.get(document.writing_style, {})

        outline_prompt = f"""Create a comprehensive {num_chapters}-chapter outline for a {document.document_type.lower()} titled "{document.title}".

Document Specifications:
- Total pages: {total_pages}
- Target words per page: {words_per_page}
- Total target words: {total_words}
- Number of chapters: {num_chapters}
- Writing style: {document.writing_style}

{style_def.get('description', '')}

Create a logical chapter structure with appropriate page distribution:
- Introduction chapter: 1-2 pages
- Core content chapters: Distribute remaining pages evenly
- Conclusion chapter: 1-2 pages

For each chapter, provide:
1. Chapter title
2. Suggested page count
3. Brief description of content focus

Format your response as:
Chapter 1: [Title] | [Pages] | [Description]
Chapter 2: [Title] | [Pages] | [Description]
...

Ensure the total pages add up to exactly {total_pages} pages."""

        response = await self.call_ollama_generate_with_fallback(outline_prompt)
        
        # Parse chapter outline from response
        lines = [line.strip() for line in response.split('\n') if line.strip()]
        chapters = []

        for line in lines:
            # Parse format: "Chapter X: Title | Pages | Description"
            if '|' in line and 'Chapter' in line:
                parts = line.split('|')
                if len(parts) >= 2:
                    # Extract title (remove "Chapter X:" prefix)
                    title_part = parts[0].strip()
                    title = re.sub(r'^Chapter\s*\d+[:.]\s*', '', title_part, flags=re.IGNORECASE).strip()

                    # Extract page count
                    try:
                        pages = int(re.search(r'\d+', parts[1]).group())
                    except:
                        pages = max(1, total_pages // num_chapters)  # Default distribution

                    # Extract description if available
                    description = parts[2].strip() if len(parts) > 2 else ""

                    chapters.append({
                        'title': title,
                        'pages': pages,
                        'description': description,
                        'target_words': pages * words_per_page
                    })

        # Fallback if parsing failed - create simple structure
        if len(chapters) < 3:
            pages_per_chapter = max(1, total_pages // num_chapters)
            remaining_pages = total_pages

            # Create basic structure
            chapters = []
            chapter_names = [
                'Introduction and Overview',
                'Background and Context',
                'Core Analysis and Discussion',
                'Applications and Implications',
                'Conclusion and Future Directions'
            ]

            # Distribute pages
            for i, name in enumerate(chapter_names[:num_chapters]):
                if i == 0 or i == len(chapter_names) - 1:  # First and last chapters
                    pages = min(2, remaining_pages)
                else:  # Middle chapters get more pages
                    pages = min(pages_per_chapter, remaining_pages)

                chapters.append({
                    'title': name,
                    'pages': pages,
                    'target_words': pages * words_per_page
                })
                remaining_pages -= pages

            # Distribute any remaining pages to middle chapters
            while remaining_pages > 0 and len(chapters) > 2:
                for ch in chapters[1:-1]:  # Skip first and last
                    if remaining_pages > 0:
                        ch['pages'] += 1
                        ch['target_words'] = ch['pages'] * words_per_page
                        remaining_pages -= 1

        return chapters
    
    async def run_generation_flow(self, document_id: int, db: Session):
        """Main generation flow - orchestrates the entire document creation process"""

        # Log to file for debugging
        with open("generation_debug.log", "a", encoding="utf-8") as f:
            f.write(f"Starting generation flow for document {document_id}\n")

        # Get document from database
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            with open("generation_debug.log", "a", encoding="utf-8") as f:
                f.write(f"Document {document_id} not found\n")
            return

        with open("generation_debug.log", "a", encoding="utf-8") as f:
            f.write(f"Found document: {document.title}\n")

        try:
            # Update status to generating
            document.status = "generating"
            document.progress = 0.0
            db.commit()
            with open("generation_debug.log", "a", encoding="utf-8") as f:
                f.write(f"Updated document status to generating\n")
            
            # Clear any existing memory for this document (RAG disabled)
            # self.rag_service.clear_document_memory(document_id)
            
            # Generate document outline with page distribution
            with open("generation_debug.log", "a", encoding="utf-8") as f:
                f.write(f"Generating document outline...\n")
            chapter_outline = await self.generate_document_outline(document)
            with open("generation_debug.log", "a", encoding="utf-8") as f:
                f.write(f"Generated {len(chapter_outline)} chapters with page distribution\n")

            # Create chapter records in database
            for i, chapter_info in enumerate(chapter_outline):
                chapter = Chapter(
                    document_id=document_id,
                    chapter_number=i + 1,
                    title=chapter_info['title'],
                    status="pending",
                    target_words=chapter_info['target_words']
                )
                db.add(chapter)
            db.commit()
            with open("generation_debug.log", "a", encoding="utf-8") as f:
                f.write(f"Created {len(chapter_outline)} chapter records in database\n")
            
            # Generate each chapter
            chapters = db.query(Chapter).filter(Chapter.document_id == document_id).order_by(Chapter.chapter_number).all()
            with open("generation_debug.log", "a", encoding="utf-8") as f:
                f.write(f"Starting generation of {len(chapters)} chapters\n")

            for i, chapter in enumerate(chapters):
                try:
                    with open("generation_debug.log", "a", encoding="utf-8") as f:
                        f.write(f"Generating Chapter {chapter.chapter_number}: {chapter.title}\n")
                    # Update chapter status
                    chapter.status = "generating"
                    db.commit()
                    
                    # Get relevant context from previous chapters (RAG disabled)
                    # relevant_context = await self.rag_service.retrieve_relevant_context(
                    #     chapter.title, document_id, n_results=3
                    # )
                    relevant_context = None
                    
                    # Get previous chapter content for flow continuity
                    previous_chapter = db.query(Chapter).filter(
                        Chapter.document_id == document_id,
                        Chapter.chapter_number == chapter.chapter_number - 1,
                        Chapter.status == "completed"
                    ).first()

                    previous_content = previous_chapter.content[-500:] if previous_chapter and previous_chapter.content else None

                    # Generate chapter content with target word count and flow
                    prompt = self.construct_enhanced_prompt(
                        document,
                        chapter.title,
                        chapter.chapter_number,
                        chapter.target_words,
                        previous_content,
                        relevant_context
                    )
                    content = await self.call_ollama_generate_with_fallback(prompt)

                    # Update chapter with generated content
                    chapter.content = content
                    chapter.word_count = len(content.split())
                    chapter.status = "completed"
                    db.commit()

                    with open("generation_debug.log", "a", encoding="utf-8") as f:
                        f.write(f"Completed Chapter {chapter.chapter_number} ({chapter.word_count} words)\n")
                    
                    # Add chapter content to RAG memory (RAG disabled)
                    # await self.rag_service.add_text_to_memory(
                    #     content, document_id, chapter.id, chapter.title
                    # )
                    
                    # Update document progress
                    document.progress = ((i + 1) / len(chapters)) * 100
                    db.commit()
                    
                    # 6-second delay between chapters as specified
                    await asyncio.sleep(6)
                    
                except Exception as e:
                    print(f"Error generating chapter {chapter.chapter_number}: {e}")
                    chapter.status = "error"
                    db.commit()
            
            # Mark document as completed
            document.status = "completed"
            document.progress = 100.0
            db.commit()

            with open("generation_debug.log", "a", encoding="utf-8") as f:
                f.write(f"Document generation completed successfully\n")
            
        except Exception as e:
            print(f"Error in generation flow: {e}")
            document.status = "error"
            db.commit()
    
    async def check_grammar(self, text: str) -> str:
        """Check and correct grammar using Ollama"""
        if not text or len(text.strip()) == 0:
            return text
        
        prompt = f"{GRAMMATICAL_CORRECTION_PROTOCOL}\n\nText to correct:\n{text}"
        
        try:
            corrected = await self.call_ollama_generate(prompt)
            return corrected.strip()
        except Exception as e:
            print(f"Error checking grammar: {e}")
            return text  # Return original text if correction fails
