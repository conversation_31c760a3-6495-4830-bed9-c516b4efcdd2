"""
Generation Service - Core document generation logic
Orchestrates the RAG-powered document creation process
"""

import httpx
import asyncio
import json
import requests
from typing import Dict, Any, List
from sqlalchemy.orm import Session
from app.db.models import Document, Chapter, AcademicFormattingSettings
from app.core.rag_service import RAGService
from app.core.protocols import (
    WRITING_STYLE_DEFINITIONS, 
    CULTURAL_INFLECTION_DEFINITIONS,
    HUMANIZATION_PROTOCOL,
    GRAMMATICAL_CORRECTION_PROTOCOL,
    get_academic_formatting_protocol,
    get_word_count_estimate
)

class GenerationService:
    def __init__(self):
        self.ollama_base_url = "http://localhost:11434"
        self.generation_model = "deepseek-r1:1.5b"  # Primary model for reasoning tasks
        self.fallback_model = "llama3.2:3b"  # Fallback model for direct generation
        self.rag_service = RAGService()
    
    async def call_ollama_generate(self, prompt: str, model: str = None) -> str:
        """Enhanced Ollama API call with better error handling"""
        if model is None:
            model = self.generation_model

        try:
            # Increased token limit and adjusted parameters
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json={
                    "model": model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "top_p": 0.9,
                        "top_k": 40,
                        "num_predict": 600,      # Increased from 400
                        "stop": ["<think>", "<thinking>", "Thinking:", "Analysis:"],  # Stop on reasoning
                        "repeat_penalty": 1.1,
                        "seed": -1
                    }
                },
                timeout=120.0  # Increased timeout
            )

            response.raise_for_status()
            result = response.json()
            content = result.get("response", "")

            if not content or len(content.strip()) < 50:
                return "Unable to generate sufficient content. Please check Ollama model availability."

            # Enhanced content filtering
            content = self.filter_reasoning_content(content)

            # Additional validation
            if len(content.strip()) < 100:
                return f"Generated content too short. Raw content: {content[:200]}..."

            return content

        except requests.exceptions.Timeout:
            return "Generation timed out. Please try again."
        except requests.exceptions.ConnectionError:
            return "Cannot connect to Ollama. Please ensure it's running."
        except Exception as e:
            print(f"Error calling Ollama: {e}")
            return f"Error generating content: {str(e)}"

    async def call_ollama_generate_with_fallback(self, prompt: str) -> str:
        """Try primary model first, fallback to simpler model if needed"""

        # Try primary model
        try:
            content = await self.call_ollama_generate(prompt, self.generation_model)

            # If content has reasoning artifacts, try fallback
            if "<think>" in content.lower() or "thinking" in content.lower()[:100]:
                print("Primary model returned reasoning content, trying fallback...")
                content = await self.call_ollama_generate(prompt, self.fallback_model)

            return content

        except Exception as e:
            print(f"Primary model failed: {e}, trying fallback...")
            return await self.call_ollama_generate(prompt, self.fallback_model)

    def filter_reasoning_content(self, content: str) -> str:
        """Enhanced filter for DeepSeek reasoning/thinking process"""
        import re

        # Remove all thinking tags (multiple variations)
        thinking_patterns = [
            r'<think>.*?</think>',
            r'<thinking>.*?</thinking>',
            r'<reason>.*?</reason>',
            r'<reasoning>.*?</reasoning>'
        ]

        for pattern in thinking_patterns:
            content = re.sub(pattern, '', content, flags=re.DOTALL | re.IGNORECASE)

        # Remove meta-commentary patterns
        meta_patterns = [
            r'^.*?(?:I need to|I should|I will|Let me|I\'ll).*?$',
            r'^.*?(?:The user|The task|The document|The chapter).*?$',
            r'^.*?(?:Looking at|Considering|Given that|Based on).*?$',
            r'^.*?(?:So |Now |Next |Then |First,|Second,|Third,).*?$',
            r'^\d+\.\s*(?:First|Second|Third|Fourth|Fifth).*?$',
            r'^.*?(?:step|approach|strategy|method).*?:.*?$'
        ]

        lines = content.split('\n')
        filtered_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                filtered_lines.append('')
                continue

            # Check against meta-commentary patterns
            is_meta = False
            for pattern in meta_patterns:
                if re.match(pattern, line, re.IGNORECASE):
                    is_meta = True
                    break

            if not is_meta:
                filtered_lines.append(line)

        # Join and clean up
        result = '\n'.join(filtered_lines)
        result = re.sub(r'\n\s*\n\s*\n+', '\n\n', result)

        # Additional cleanup - remove any remaining thinking indicators
        result = re.sub(r'(?:^|\n)(?:Thinking|Reasoning|Analysis):\s*', '', result, flags=re.IGNORECASE)

        return result.strip()
    
    def construct_prompt(self, document: Document, chapter_title: str, chapter_number: int, 
                        relevant_context: List[Dict[str, Any]] = None) -> str:
        """Construct the complete prompt for chapter generation"""
        
        # Get writing style and cultural inflection
        style_def = WRITING_STYLE_DEFINITIONS.get(document.writing_style, {})
        culture_def = CULTURAL_INFLECTION_DEFINITIONS.get(document.cultural_inflection, {})
        
        # Build context section (simplified)
        context_section = ""
        if relevant_context:
            context_texts = [chunk["text"][:300] for chunk in relevant_context]  # Limit context length
            context_section = f"""
PREVIOUS CONTENT CONTEXT:
{chr(10).join([f"- {text}" for text in context_texts])}
"""
        
        # Academic formatting
        academic_settings = AcademicFormattingSettings(
            enable_academic_formatting=document.enable_academic_formatting,
            citation_style=document.citation_style,
            student_name=document.student_name,
            course_name=document.course_name,
            instructor_name=document.instructor_name,
            due_date=document.due_date
        )
        academic_formatting = get_academic_formatting_protocol(academic_settings)
        
        # Humanization protocol
        humanization = HUMANIZATION_PROTOCOL if document.improve_realism else ""
        
        # Simplified, direct prompt
        prompt = f"""Write the complete content for Chapter {chapter_number} titled "{chapter_title}" for the {document.document_type.lower()} "{document.title}".

{context_section}

REQUIREMENTS:
- Write exactly {max(300, document.estimated_words // 10)} words
- Use {document.writing_style} writing style
- Apply {document.cultural_inflection} perspective
- Write in complete paragraphs with detailed explanations
- Include examples and thorough coverage of the topic
- Make it engaging and well-structured

IMPORTANT: Write ONLY the chapter content. No thinking, no reasoning, no meta-commentary. Start immediately with the first paragraph.

Chapter content:"""
        
        return prompt
    
    async def generate_chapter_outline(self, document: Document) -> List[str]:
        """Generate chapter titles/outline for the document"""
        
        style_def = WRITING_STYLE_DEFINITIONS.get(document.writing_style, {})
        
        outline_prompt = f"""
Generate a detailed chapter outline for a {document.document_type.lower()} titled "{document.title}".

Writing Style: {document.writing_style}
Style Description: {style_def.get('DESCRIPTION', '')}

Document Type: {document.document_type}
Target Pages: {document.pages}
Estimated Words: {document.estimated_words}

Create exactly 10 chapter titles that would be appropriate for this document. Each chapter should be substantial and contribute meaningfully to the overall work.

Return only the chapter titles, one per line, without numbers or additional formatting:
"""
        
        response = await self.call_ollama_generate_with_fallback(outline_prompt)
        
        # Parse chapter titles from response
        lines = [line.strip() for line in response.split('\n') if line.strip()]
        chapter_titles = []
        
        for line in lines:
            # Remove any numbering or formatting
            clean_title = line.strip()
            if clean_title.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '10.')):
                clean_title = clean_title.split('.', 1)[1].strip()
            elif clean_title.startswith(('Chapter', 'chapter')):
                parts = clean_title.split(':', 1)
                if len(parts) > 1:
                    clean_title = parts[1].strip()
            
            if clean_title and len(clean_title) > 5:  # Ensure meaningful titles
                chapter_titles.append(clean_title)
        
        # Ensure we have exactly 10 chapters
        if len(chapter_titles) < 10:
            # Add generic chapters if needed
            for i in range(len(chapter_titles), 10):
                chapter_titles.append(f"Chapter {i+1} Content")
        elif len(chapter_titles) > 10:
            chapter_titles = chapter_titles[:10]
        
        return chapter_titles
    
    async def run_generation_flow(self, document_id: int, db: Session):
        """Main generation flow - orchestrates the entire document creation process"""
        
        # Get document from database
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            return
        
        try:
            # Update status to generating
            document.status = "generating"
            document.progress = 0.0
            db.commit()
            
            # Clear any existing memory for this document
            self.rag_service.clear_document_memory(document_id)
            
            # Generate chapter outline
            chapter_titles = await self.generate_chapter_outline(document)
            
            # Create chapter records in database
            for i, title in enumerate(chapter_titles):
                chapter = Chapter(
                    document_id=document_id,
                    chapter_number=i + 1,
                    title=title,
                    status="pending"
                )
                db.add(chapter)
            db.commit()
            
            # Generate each chapter
            chapters = db.query(Chapter).filter(Chapter.document_id == document_id).order_by(Chapter.chapter_number).all()
            
            for i, chapter in enumerate(chapters):
                try:
                    # Update chapter status
                    chapter.status = "generating"
                    db.commit()
                    
                    # Get relevant context from previous chapters
                    relevant_context = await self.rag_service.retrieve_relevant_context(
                        chapter.title, document_id, n_results=3
                    )
                    
                    # Generate chapter content with fallback
                    prompt = self.construct_prompt(document, chapter.title, chapter.chapter_number, relevant_context)
                    content = await self.call_ollama_generate_with_fallback(prompt)
                    
                    # Update chapter with generated content
                    chapter.content = content
                    chapter.word_count = len(content.split())
                    chapter.status = "completed"
                    db.commit()
                    
                    # Add chapter content to RAG memory
                    await self.rag_service.add_text_to_memory(
                        content, document_id, chapter.id, chapter.title
                    )
                    
                    # Update document progress
                    document.progress = ((i + 1) / len(chapters)) * 100
                    db.commit()
                    
                    # 6-second delay between chapters as specified
                    await asyncio.sleep(6)
                    
                except Exception as e:
                    print(f"Error generating chapter {chapter.chapter_number}: {e}")
                    chapter.status = "error"
                    db.commit()
            
            # Mark document as completed
            document.status = "completed"
            document.progress = 100.0
            db.commit()
            
        except Exception as e:
            print(f"Error in generation flow: {e}")
            document.status = "error"
            db.commit()
    
    async def check_grammar(self, text: str) -> str:
        """Check and correct grammar using Ollama"""
        if not text or len(text.strip()) == 0:
            return text
        
        prompt = f"{GRAMMATICAL_CORRECTION_PROTOCOL}\n\nText to correct:\n{text}"
        
        try:
            corrected = await self.call_ollama_generate(prompt)
            return corrected.strip()
        except Exception as e:
            print(f"Error checking grammar: {e}")
            return text  # Return original text if correction fails
