@echo off
title ASCAES - Fix Dependencies
color 0A

echo.
echo ========================================
echo   ASCAES - Fixing Dependencies
echo ========================================
echo.

echo Installing/Updating all dependencies...
echo.

REM Install root dependencies
echo [1/3] Installing root dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install root dependencies
    pause
    exit /b 1
)

REM Install frontend dependencies
echo [2/3] Installing frontend dependencies...
cd frontend-react
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

REM Install Python dependencies
echo [3/3] Installing Python dependencies...
cd backend-python
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERROR: Failed to install Python dependencies
    pause
    exit /b 1
)

REM Install specific missing packages
echo Installing additional packages...
pip install reportlab pillow
cd ..

echo.
echo ========================================
echo   Dependencies Fixed Successfully!
echo ========================================
echo.
echo You can now run: run-ascaes.bat
echo.
pause
