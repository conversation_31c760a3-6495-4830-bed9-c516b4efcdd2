@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-dark-border;
  }
  
  body {
    @apply bg-dark-bg text-dark-text;
  }
  
  /* Custom scrollbar for dark theme */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-dark-bg;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-dark-border rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-dark-text-secondary;
  }
}

@layer components {
  .btn-primary {
    @apply bg-dark-accent hover:bg-dark-accent-hover text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-secondary {
    @apply bg-dark-panel hover:bg-dark-border text-dark-text font-medium px-4 py-2 rounded-lg border border-dark-border transition-colors duration-200;
  }
  
  .input-field {
    @apply bg-dark-panel border border-dark-border rounded-lg px-3 py-2 text-dark-text placeholder-dark-text-secondary focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent transition-all duration-200;
  }
  
  .select-field {
    @apply bg-dark-panel border border-dark-border rounded-lg px-3 py-2 text-dark-text focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent transition-all duration-200 cursor-pointer;
  }
  
  .card {
    @apply bg-dark-panel border border-dark-border rounded-lg p-6 shadow-lg;
  }
  
  .toggle-switch {
    @apply relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-dark-accent focus:ring-offset-2 focus:ring-offset-dark-bg;
  }
  
  .toggle-switch.enabled {
    @apply bg-dark-accent;
  }
  
  .toggle-switch.disabled {
    @apply bg-dark-border;
  }
  
  .toggle-thumb {
    @apply inline-block h-4 w-4 transform rounded-full bg-white transition-transform;
  }
  
  .toggle-thumb.enabled {
    @apply translate-x-6;
  }
  
  .toggle-thumb.disabled {
    @apply translate-x-1;
  }
}
