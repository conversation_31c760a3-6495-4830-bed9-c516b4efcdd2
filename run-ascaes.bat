@echo off
title ASCAES - Academic Document Generation Specialist
color 0A

echo.
echo ========================================
echo   ASCAES - Academic Document Generator
echo ========================================
echo.

REM Check if in correct directory
if not exist "package.json" (
    echo ERROR: Please run this script from the ASCAES project directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org/
    pause
    exit /b 1
)

echo Prerequisites found - OK
echo.

REM Check if dependencies are installed
if not exist "node_modules" (
    echo Installing dependencies for the first time...
    echo This may take a few minutes...
    call npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

if not exist "frontend-react\node_modules" (
    echo Installing frontend dependencies...
    cd frontend-react
    call npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install frontend dependencies
        pause
        exit /b 1
    )
    cd ..
)

REM Check and install Python dependencies
echo Checking Python dependencies...
cd backend-python
pip install -r requirements.txt >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing Python dependencies...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install Python dependencies
        pause
        exit /b 1
    )
)
cd ..

echo.
echo Checking Ollama connection...
curl -s http://localhost:11434/api/version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ============================================
    echo   WARNING: Ollama is not running!
    echo ============================================
    echo.
    echo Ollama is required for AI functionality.
    echo Please start Ollama in another terminal:
    echo   ollama serve
    echo.
    echo Then make sure you have the required models:
    echo   ollama pull deepseek-r1:1.5b
    echo   ollama pull nomic-embed-text
    echo.
    echo Press any key to continue anyway...
    pause >nul
) else (
    echo Ollama is running - OK
)

echo.
echo ========================================
echo   Starting ASCAES Application
echo ========================================
echo.
echo This will start:
echo - Python Backend (Port 8000)
echo - React Frontend (Port 5173)  
echo - Electron Desktop App
echo.
echo Press Ctrl+C to stop all services
echo.

REM Start all services using npm dev command
npm run dev

echo.
echo ASCAES has been stopped.
pause
