"""
Generation API Routes
Handles document generation and grammar checking endpoints
"""

from fastapi import APIRouter, Depends, BackgroundTasks, HTTPException
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.db.models import GenerationRequest, GrammarCheckRequest, GrammarCheckResponse
from app.core.generation_service import GenerationService

router = APIRouter()
generation_service = GenerationService()

def run_generation_wrapper(document_id: int, db: Session):
    """Wrapper to run async generation in background task"""
    import asyncio
    from app.db.database import SessionLocal

    # Log that the wrapper is being called
    with open("generation_debug.log", "a", encoding="utf-8") as f:
        f.write(f"run_generation_wrapper called for document {document_id}\n")

    # Create a new database session for the background task
    background_db = SessionLocal()

    try:
        # Create new event loop for background task
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        with open("generation_debug.log", "a", encoding="utf-8") as f:
            f.write(f"Starting async generation flow\n")
        loop.run_until_complete(generation_service.run_generation_flow(document_id, background_db))
    except Exception as e:
        with open("generation_debug.log", "a", encoding="utf-8") as f:
            f.write(f"Error in background generation: {e}\n")
        # Update document status to error
        from app.db.models import Document
        document = background_db.query(Document).filter(Document.id == document_id).first()
        if document:
            document.status = "error"
            background_db.commit()
    finally:
        background_db.close()
        loop.close()

@router.post("/generate")
async def start_generation(
    request: GenerationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Start document generation process
    Creates document and chapter placeholders, then launches generation as background task
    """
    
    # Verify document exists
    from app.db.models import Document
    document = db.query(Document).filter(Document.id == request.document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Check if document is already being generated
    if document.status == "generating":
        raise HTTPException(status_code=400, detail="Document is already being generated")
    
    # Log that we're starting the background task
    with open("generation_debug.log", "a", encoding="utf-8") as f:
        f.write(f"Adding background task for document {request.document_id}\n")

    # Start generation as background task (don't pass db session)
    background_tasks.add_task(run_generation_wrapper, request.document_id, None)
    
    return {
        "message": "Generation started",
        "document_id": request.document_id,
        "status": "generating"
    }

@router.post("/check-grammar", response_model=GrammarCheckResponse)
async def check_grammar(request: GrammarCheckRequest):
    """
    Check and correct grammar for provided text
    """
    
    if not request.text or len(request.text.strip()) == 0:
        return GrammarCheckResponse(corrected_text=request.text)
    
    try:
        corrected_text = await generation_service.check_grammar(request.text)
        return GrammarCheckResponse(corrected_text=corrected_text)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Grammar check failed: {str(e)}")

@router.get("/generation-status/{document_id}")
async def get_generation_status(document_id: int, db: Session = Depends(get_db)):
    """
    Get the current generation status and progress for a document
    """
    
    from app.db.models import Document, Chapter
    
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Get chapter statuses
    chapters = db.query(Chapter).filter(Chapter.document_id == document_id).order_by(Chapter.chapter_number).all()
    
    chapter_statuses = [
        {
            "chapter_number": ch.chapter_number,
            "title": ch.title,
            "status": ch.status,
            "word_count": ch.word_count
        }
        for ch in chapters
    ]
    
    return {
        "document_id": document_id,
        "status": document.status,
        "progress": document.progress,
        "chapters": chapter_statuses,
        "total_chapters": len(chapters)
    }
