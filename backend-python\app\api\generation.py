"""
Generation API Routes
Handles document generation and grammar checking endpoints
"""

from fastapi import <PERSON>Rout<PERSON>, Depends, BackgroundTasks, HTTPException
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.db.models import GenerationRequest, GrammarCheckRequest, GrammarCheckResponse
from app.core.generation_service import GenerationService

router = APIRouter()
generation_service = GenerationService()

@router.post("/generate")
async def start_generation(
    request: GenerationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Start document generation process
    Creates document and chapter placeholders, then launches generation as background task
    """
    
    # Verify document exists
    from app.db.models import Document
    document = db.query(Document).filter(Document.id == request.document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Check if document is already being generated
    if document.status == "generating":
        raise HTTPException(status_code=400, detail="Document is already being generated")
    
    # Start generation as background task
    background_tasks.add_task(generation_service.run_generation_flow, request.document_id, db)
    
    return {
        "message": "Generation started",
        "document_id": request.document_id,
        "status": "generating"
    }

@router.post("/check-grammar", response_model=GrammarCheckResponse)
async def check_grammar(request: GrammarCheckRequest):
    """
    Check and correct grammar for provided text
    """
    
    if not request.text or len(request.text.strip()) == 0:
        return GrammarCheckResponse(corrected_text=request.text)
    
    try:
        corrected_text = await generation_service.check_grammar(request.text)
        return GrammarCheckResponse(corrected_text=corrected_text)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Grammar check failed: {str(e)}")

@router.get("/generation-status/{document_id}")
async def get_generation_status(document_id: int, db: Session = Depends(get_db)):
    """
    Get the current generation status and progress for a document
    """
    
    from app.db.models import Document, Chapter
    
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Get chapter statuses
    chapters = db.query(Chapter).filter(Chapter.document_id == document_id).order_by(Chapter.chapter_number).all()
    
    chapter_statuses = [
        {
            "chapter_number": ch.chapter_number,
            "title": ch.title,
            "status": ch.status,
            "word_count": ch.word_count
        }
        for ch in chapters
    ]
    
    return {
        "document_id": document_id,
        "status": document.status,
        "progress": document.progress,
        "chapters": chapter_statuses,
        "total_chapters": len(chapters)
    }
