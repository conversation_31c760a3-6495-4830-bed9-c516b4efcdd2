/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Helios Dark Theme
        dark: {
          bg: '#0d1117',
          panel: '#161b22',
          border: '#30363d',
          text: '#e6edf3',
          'text-secondary': '#8b949e',
          accent: '#58a6ff',
          'accent-hover': '#4493e2',
          success: '#3fb950',
          warning: '#d29922',
          error: '#f85149'
        },
        // Helios Light Theme
        light: {
          bg: '#ffffff',
          panel: '#f6f8fa',
          border: '#d0d7de',
          text: '#24292f',
          'text-secondary': '#656d76',
          accent: '#0969da',
          'accent-hover': '#0860ca',
          success: '#1a7f37',
          warning: '#9a6700',
          error: '#cf222e'
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Fira Code', 'monospace']
      },
      animation: {
        'fade-in': 'fadeIn 0.2s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
        'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite'
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        slideIn: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        }
      }
    },
  },
  plugins: [],
}
