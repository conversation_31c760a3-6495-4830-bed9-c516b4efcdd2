#!/usr/bin/env python3
"""
Migration script to add target_words column to chapters table
"""

import sqlite3
import os

def migrate_database():
    """Add target_words column to chapters table if it doesn't exist"""
    
    db_path = "documents.db"
    
    if not os.path.exists(db_path):
        print("Database file not found. Will be created on next startup.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if target_words column exists
        cursor.execute("PRAGMA table_info(chapters)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'target_words' not in columns:
            print("Adding target_words column to chapters table...")
            cursor.execute("ALTER TABLE chapters ADD COLUMN target_words INTEGER DEFAULT 400")
            
            # Update existing chapters with reasonable target words based on document pages
            cursor.execute("""
                UPDATE chapters 
                SET target_words = (
                    SELECT CASE 
                        WHEN d.pages <= 10 THEN 300
                        WHEN d.pages <= 50 THEN 400
                        ELSE 500
                    END
                    FROM documents d 
                    WHERE d.id = chapters.document_id
                )
            """)
            
            conn.commit()
            print("✅ Successfully added target_words column and updated existing chapters")
        else:
            print("✅ target_words column already exists")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")

if __name__ == "__main__":
    migrate_database()
