import React, { useState, useEffect } from 'react'

interface CulturalInflectionSliderProps {
  value: { [key: string]: number }
  onChange: (value: { [key: string]: number }) => void
}

const CULTURAL_OPTIONS = [
  'American',
  'Russian',
  'German',
  'Japanese',
  'French',
  'Italian'
]

const CulturalInflectionSlider: React.FC<CulturalInflectionSliderProps> = ({ value, onChange }) => {
  const [selectedCultures, setSelectedCultures] = useState<string[]>([])
  const [percentages, setPercentages] = useState<{ [key: string]: number }>({})

  // Initialize with existing values or defaults
  useEffect(() => {
    const cultures = Object.keys(value).filter(key => value[key] > 0)
    if (cultures.length === 0) {
      // Default to American 100%
      setSelectedCultures(['American'])
      setPercentages({ 'American': 100 })
      onChange({ 'American': 100 })
    } else {
      setSelectedCultures(cultures)
      setPercentages(value)
    }
  }, [])

  const handleCultureToggle = (culture: string) => {
    if (selectedCultures.includes(culture)) {
      // Remove culture if more than 1 selected
      if (selectedCultures.length > 1) {
        const newSelected = selectedCultures.filter(c => c !== culture)
        const newPercentages = { ...percentages }
        delete newPercentages[culture]
        
        // Redistribute percentages
        const remaining = newSelected.length
        const equalShare = Math.floor(100 / remaining)
        let total = 0
        
        newSelected.forEach((c, index) => {
          if (index === newSelected.length - 1) {
            // Last item gets the remainder
            newPercentages[c] = 100 - total
          } else {
            newPercentages[c] = equalShare
            total += equalShare
          }
        })
        
        setSelectedCultures(newSelected)
        setPercentages(newPercentages)
        onChange(newPercentages)
      }
    } else {
      // Add culture if less than 2 selected
      if (selectedCultures.length < 2) {
        const newSelected = [...selectedCultures, culture]
        const newPercentages = { ...percentages }
        
        if (newSelected.length === 2) {
          // Split 50/50
          newPercentages[newSelected[0]] = 50
          newPercentages[culture] = 50
        } else {
          newPercentages[culture] = 100
        }
        
        setSelectedCultures(newSelected)
        setPercentages(newPercentages)
        onChange(newPercentages)
      }
    }
  }

  const handleSliderChange = (culture: string, newValue: number) => {
    if (selectedCultures.length === 2) {
      // Ensure values stay within 30-70 range
      const clampedValue = Math.max(30, Math.min(70, newValue))
      const otherCulture = selectedCultures.find(c => c !== culture)!
      const otherValue = 100 - clampedValue
      
      const newPercentages = {
        [culture]: clampedValue,
        [otherCulture]: otherValue
      }
      
      setPercentages(newPercentages)
      onChange(newPercentages)
    }
  }

  return (
    <div className="space-y-4">
      <div className="text-sm text-dark-text-secondary mb-4">
        Select up to 2 cultural influences. Total must equal 100%.
        {selectedCultures.length === 2 && (
          <span className="block mt-1">Range: 30% minimum, 70% maximum per culture.</span>
        )}
      </div>

      <div className="space-y-3">
        {CULTURAL_OPTIONS.map((culture) => {
          const isSelected = selectedCultures.includes(culture)
          const percentage = percentages[culture] || 0
          
          return (
            <div key={culture} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => handleCultureToggle(culture)}
                    className={`w-5 h-5 rounded-full border-2 transition-colors ${
                      isSelected
                        ? 'bg-dark-accent border-dark-accent'
                        : 'border-dark-border hover:border-dark-accent'
                    }`}
                  >
                    {isSelected && (
                      <div className="w-full h-full rounded-full bg-white scale-50"></div>
                    )}
                  </button>
                  <span className="font-medium text-dark-text">{culture}</span>
                </div>
                
                {isSelected && (
                  <span className="text-sm font-medium text-dark-text">
                    {percentage}%
                  </span>
                )}
              </div>
              
              {isSelected && selectedCultures.length === 2 && (
                <div className="ml-8">
                  <input
                    type="range"
                    min="30"
                    max="70"
                    value={percentage}
                    onChange={(e) => handleSliderChange(culture, parseInt(e.target.value))}
                    className="w-full h-2 bg-dark-border rounded-lg appearance-none cursor-pointer slider"
                    style={{
                      background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${percentage}%, #374151 ${percentage}%, #374151 100%)`
                    }}
                  />
                  <div className="flex justify-between text-xs text-dark-text-secondary mt-1">
                    <span>30%</span>
                    <span>70%</span>
                  </div>
                </div>
              )}
              
              {isSelected && selectedCultures.length === 1 && (
                <div className="ml-8">
                  <div className="w-full h-2 bg-dark-accent rounded-lg"></div>
                  <div className="text-center text-xs text-dark-text-secondary mt-1">
                    100%
                  </div>
                </div>
              )}
            </div>
          )
        })}
      </div>
      
      {selectedCultures.length === 2 && (
        <div className="mt-4 p-3 bg-dark-bg rounded-lg">
          <div className="text-sm text-dark-text-secondary">
            Current distribution:
          </div>
          <div className="flex space-x-4 mt-2">
            {selectedCultures.map((culture) => (
              <div key={culture} className="flex items-center space-x-2">
                <span className="text-sm font-medium text-dark-text">{culture}:</span>
                <span className="text-sm text-dark-accent">{percentages[culture]}%</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default CulturalInflectionSlider
