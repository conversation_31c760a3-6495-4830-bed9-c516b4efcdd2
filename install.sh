#!/bin/bash

echo "Installing ASCAES - Academic Document Generation Specialist"
echo

echo "Step 1: Installing Node.js dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "Error: Failed to install Node.js dependencies"
    exit 1
fi

echo
echo "Step 2: Installing frontend dependencies..."
cd frontend-react
npm install
if [ $? -ne 0 ]; then
    echo "Error: Failed to install frontend dependencies"
    exit 1
fi
cd ..

echo
echo "Step 3: Installing Python dependencies..."
cd backend-python
pip3 install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "Error: Failed to install Python dependencies"
    echo "Make sure Python 3.8+ is installed and in your PATH"
    exit 1
fi
cd ..

echo
echo "Installation completed successfully!"
echo
echo "Next steps:"
echo "1. Make sure Ollama is installed and running"
echo "2. Install required models: ollama pull deepseek-r1:latest"
echo "3. Install embedding model: ollama pull nomic-embed-text"
echo "4. Run the application: npm run dev"
echo
