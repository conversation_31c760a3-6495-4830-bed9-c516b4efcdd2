#!/usr/bin/env python3
"""
Simple test to verify backend generation endpoint
"""

import requests
import time

def test_generation_endpoint():
    """Test the generation endpoint directly"""
    print("🔍 Testing Backend Generation Endpoint...")
    print("=" * 50)
    
    try:
        print("Making request to backend...")
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/models/test-generate",
            timeout=60
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response JSON: {result}")
            
            if result.get("status") == "success":
                content = result.get("response", "")
                print(f"\n✅ SUCCESS!")
                print(f"Generated Content: {content}")
                print(f"Word Count: {len(content.split())} words")
                
                # Check if reasoning was filtered
                if "<think>" in content or "Alright" in content:
                    print("⚠️ Reasoning content still present")
                else:
                    print("✅ Reasoning content filtered correctly")
                    
                return True
            else:
                print(f"❌ Generation failed: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    print("Testing backend generation with new code...")
    
    # Wait for backend to be ready
    print("Waiting for backend...")
    time.sleep(2)
    
    success = test_generation_endpoint()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Backend generation is working!")
    else:
        print("❌ Backend generation failed")

if __name__ == "__main__":
    main()
