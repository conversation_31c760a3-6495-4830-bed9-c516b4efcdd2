import { create } from 'zustand'

export interface Document {
  id: number
  title: string
  document_type: string
  spacing: string
  pages: number
  estimated_words: number
  status: string
  progress: number
  created_at: string
}

export interface Chapter {
  id: number
  chapter_number: number
  title: string
  content?: string
  word_count: number
  status: string
}

export interface DocumentFormData {
  title: string
  document_type: string
  spacing: string
  pages: number
  improve_realism: boolean
  enable_grammar_check: boolean
  writing_style: string
  cultural_inflection: string
  academic_formatting: {
    enable_academic_formatting: boolean
    citation_style?: string
    student_name?: string
    course_name?: string
    instructor_name?: string
    due_date?: string
  }
}

export interface GenerationStatus {
  document_id: number
  status: string
  progress: number
  chapters: Array<{
    chapter_number: number
    title: string
    status: string
    word_count: number
  }>
  total_chapters: number
}

interface AppState {
  // Theme
  isDarkMode: boolean
  toggleTheme: () => void

  // Documents
  documents: Document[]
  currentDocument: Document | null
  currentChapters: Chapter[]
  generationStatus: GenerationStatus | null

  // UI State
  isLoading: boolean
  error: string | null

  // Actions
  setDocuments: (documents: Document[]) => void
  setCurrentDocument: (document: Document | null) => void
  setCurrentChapters: (chapters: Chapter[]) => void
  setGenerationStatus: (status: GenerationStatus | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void

  // API Actions
  fetchDocuments: () => Promise<void>
  createDocument: (data: DocumentFormData) => Promise<Document>
  deleteDocument: (id: number) => Promise<void>
  startGeneration: (documentId: number) => Promise<void>
  fetchGenerationStatus: (documentId: number) => Promise<void>
  fetchDocumentChapters: (documentId: number) => Promise<void>
  checkGrammar: (text: string) => Promise<string>
  getWordCountEstimate: (pages: number, docType: string, spacing: string) => Promise<number>
  testConnection: () => Promise<{ backend: boolean, ollama: boolean, details: any }>
}

const API_BASE_URL = 'http://127.0.0.1:8001/api/v1'

export const useAppStore = create<AppState>((set, get) => ({
  // Initial state
  isDarkMode: true,
  documents: [],
  currentDocument: null,
  currentChapters: [],
  generationStatus: null,
  isLoading: false,
  error: null,
  
  // Theme actions
  toggleTheme: () => {
    const newTheme = !get().isDarkMode
    set({ isDarkMode: newTheme })
    
    // Update HTML class for Tailwind dark mode
    if (newTheme) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  },
  
  // State setters
  setDocuments: (documents) => set({ documents }),
  setCurrentDocument: (document) => set({ currentDocument: document }),
  setCurrentChapters: (chapters) => set({ currentChapters: chapters }),
  setGenerationStatus: (status) => set({ generationStatus: status }),
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
  
  // API actions
  fetchDocuments: async () => {
    console.log('Store: Fetching documents from:', `${API_BASE_URL}/documents`)
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${API_BASE_URL}/documents`)
      console.log('Store: Documents response status:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Store: Documents fetch failed:', response.status, errorText)
        throw new Error(`HTTP ${response.status}: ${errorText || 'Failed to fetch documents'}`)
      }

      const documents = await response.json()
      console.log('Store: Documents fetched successfully:', documents.length, 'documents')
      set({ documents, isLoading: false })
    } catch (error) {
      console.error('Store: Error in fetchDocuments:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      set({ error: errorMessage, isLoading: false })
      throw error // Re-throw so calling code can handle it
    }
  },
  
  createDocument: async (data) => {
    console.log('Store: Creating document with data:', data)
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${API_BASE_URL}/documents`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      console.log('Store: Create document response status:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Store: Create document failed:', response.status, errorText)
        throw new Error(`HTTP ${response.status}: ${errorText || 'Failed to create document'}`)
      }

      const document = await response.json()
      console.log('Store: Document created successfully:', document)

      set((state) => ({
        documents: [...state.documents, document],
        currentDocument: document,
        isLoading: false
      }))
      return document
    } catch (error) {
      console.error('Store: Error in createDocument:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      set({ error: errorMessage, isLoading: false })
      throw error
    }
  },
  
  deleteDocument: async (id) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${API_BASE_URL}/documents/${id}`, {
        method: 'DELETE'
      })
      if (!response.ok) throw new Error('Failed to delete document')
      
      // Remove from local state
      const documents = get().documents.filter(doc => doc.id !== id)
      set({ documents, isLoading: false })
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
    }
  },
  
  startGeneration: async (documentId) => {
    console.log('Store: Starting generation for document:', documentId)
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${API_BASE_URL}/generate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ document_id: documentId })
      })

      console.log('Store: Start generation response status:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Store: Start generation failed:', response.status, errorText)
        throw new Error(`HTTP ${response.status}: ${errorText || 'Failed to start generation'}`)
      }

      const result = await response.json()
      console.log('Store: Generation started successfully:', result)

      // Update document status to generating
      const { documents, currentDocument } = get()
      const updatedDocuments = documents.map(doc =>
        doc.id === documentId ? { ...doc, status: 'generating' as const } : doc
      )
      set({
        documents: updatedDocuments,
        currentDocument: currentDocument?.id === documentId
          ? { ...currentDocument, status: 'generating' as const }
          : currentDocument,
        isLoading: false
      })
    } catch (error) {
      console.error('Store: Error in startGeneration:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      set({ error: errorMessage, isLoading: false })
      throw error
    }
  },
  
  fetchGenerationStatus: async (documentId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/generation-status/${documentId}`)
      if (!response.ok) throw new Error('Failed to fetch generation status')
      const status = await response.json()

      // Update document status and progress based on generation status
      const { documents, currentDocument } = get()
      const updatedDocuments = documents.map(doc =>
        doc.id === documentId ? {
          ...doc,
          status: status.status as 'pending' | 'generating' | 'completed' | 'error',
          progress: status.progress || 0
        } : doc
      )

      set({
        generationStatus: status,
        documents: updatedDocuments,
        currentDocument: currentDocument?.id === documentId
          ? {
              ...currentDocument,
              status: status.status as 'pending' | 'generating' | 'completed' | 'error',
              progress: status.progress || 0
            }
          : currentDocument
      })
    } catch (error) {
      set({ error: (error as Error).message })
    }
  },
  
  fetchDocumentChapters: async (documentId) => {
    console.log('Store: Fetching chapters for document:', documentId)
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${API_BASE_URL}/documents/${documentId}/chapters`)
      console.log('Store: Chapters response status:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Store: Chapters fetch failed:', response.status, errorText)
        throw new Error(`HTTP ${response.status}: ${errorText || 'Failed to fetch chapters'}`)
      }

      const chapters = await response.json()
      console.log('Store: Chapters fetched successfully:', chapters.length, 'chapters')
      set({ currentChapters: chapters, isLoading: false })
    } catch (error) {
      console.error('Store: Error in fetchDocumentChapters:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      set({ error: errorMessage, isLoading: false })
      throw error
    }
  },
  
  checkGrammar: async (text) => {
    try {
      const response = await fetch(`${API_BASE_URL}/check-grammar`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text })
      })
      if (!response.ok) throw new Error('Failed to check grammar')
      const result = await response.json()
      return result.corrected_text
    } catch (error) {
      throw new Error((error as Error).message)
    }
  },
  
  getWordCountEstimate: async (pages, docType, spacing) => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/word-count-estimate?pages=${pages}&doc_type=${docType}&spacing=${spacing}`
      )
      if (!response.ok) throw new Error('Failed to get word count estimate')
      const result = await response.json()
      return result.estimated_words
    } catch (error) {
      throw new Error((error as Error).message)
    }
  },

  testConnection: async () => {
    console.log('Store: Testing connections...')
    const results = {
      backend: false,
      ollama: false,
      details: {
        backend_error: null,
        ollama_error: null,
        backend_url: API_BASE_URL,
        ollama_url: null
      }
    }

    // Test backend connection
    try {
      const backendResponse = await fetch(`${API_BASE_URL}/health`, {
        method: 'GET'
      })
      results.backend = backendResponse.ok
      if (!backendResponse.ok) {
        results.details.backend_error = `HTTP ${backendResponse.status}: ${backendResponse.statusText}`
      }
    } catch (error) {
      results.details.backend_error = error instanceof Error ? error.message : 'Connection failed'
    }

    // Test Ollama connection through backend
    try {
      const ollamaResponse = await fetch(`${API_BASE_URL}/models/status`, {
        method: 'GET'
      })
      if (ollamaResponse.ok) {
        const data = await ollamaResponse.json()
        results.ollama = data.connected === true
        results.details.ollama_url = data.ollama_url
        if (!results.ollama) {
          results.details.ollama_error = data.error || 'Ollama not connected'
        }
      } else {
        results.details.ollama_error = `HTTP ${ollamaResponse.status}: ${ollamaResponse.statusText}`
      }
    } catch (error) {
      results.details.ollama_error = error instanceof Error ? error.message : 'Connection failed'
    }

    console.log('Store: Connection test results:', results)
    return results
  }
}))
