import { create } from 'zustand'

export interface Document {
  id: number
  title: string
  document_type: string
  spacing: string
  pages: number
  estimated_words: number
  status: string
  progress: number
  created_at: string
}

export interface Chapter {
  id: number
  chapter_number: number
  title: string
  content?: string
  word_count: number
  status: string
}

export interface DocumentFormData {
  title: string
  document_type: string
  spacing: string
  pages: number
  improve_realism: boolean
  enable_grammar_check: boolean
  writing_style: string
  cultural_inflection: string
  academic_formatting: {
    enable_academic_formatting: boolean
    citation_style?: string
    student_name?: string
    course_name?: string
    instructor_name?: string
    due_date?: string
  }
}

export interface GenerationStatus {
  document_id: number
  status: string
  progress: number
  chapters: Array<{
    chapter_number: number
    title: string
    status: string
    word_count: number
  }>
  total_chapters: number
}

interface AppState {
  // Theme
  isDarkMode: boolean
  toggleTheme: () => void
  
  // Documents
  documents: Document[]
  currentDocument: Document | null
  currentChapters: Chapter[]
  generationStatus: GenerationStatus | null
  
  // UI State
  isLoading: boolean
  error: string | null
  
  // Actions
  setDocuments: (documents: Document[]) => void
  setCurrentDocument: (document: Document | null) => void
  setCurrentChapters: (chapters: Chapter[]) => void
  setGenerationStatus: (status: GenerationStatus | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  
  // API Actions
  fetchDocuments: () => Promise<void>
  createDocument: (data: DocumentFormData) => Promise<Document>
  deleteDocument: (id: number) => Promise<void>
  startGeneration: (documentId: number) => Promise<void>
  fetchGenerationStatus: (documentId: number) => Promise<void>
  fetchDocumentChapters: (documentId: number) => Promise<void>
  checkGrammar: (text: string) => Promise<string>
  getWordCountEstimate: (pages: number, docType: string, spacing: string) => Promise<number>
}

const API_BASE_URL = 'http://127.0.0.1:8000/api/v1'

export const useAppStore = create<AppState>((set, get) => ({
  // Initial state
  isDarkMode: true,
  documents: [],
  currentDocument: null,
  currentChapters: [],
  generationStatus: null,
  isLoading: false,
  error: null,
  
  // Theme actions
  toggleTheme: () => {
    const newTheme = !get().isDarkMode
    set({ isDarkMode: newTheme })
    
    // Update HTML class for Tailwind dark mode
    if (newTheme) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  },
  
  // State setters
  setDocuments: (documents) => set({ documents }),
  setCurrentDocument: (document) => set({ currentDocument: document }),
  setCurrentChapters: (chapters) => set({ currentChapters: chapters }),
  setGenerationStatus: (status) => set({ generationStatus: status }),
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
  
  // API actions
  fetchDocuments: async () => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${API_BASE_URL}/documents`)
      if (!response.ok) throw new Error('Failed to fetch documents')
      const documents = await response.json()
      set({ documents, isLoading: false })
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
    }
  },
  
  createDocument: async (data) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${API_BASE_URL}/documents`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      if (!response.ok) throw new Error('Failed to create document')
      const document = await response.json()
      set({ isLoading: false })
      return document
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
      throw error
    }
  },
  
  deleteDocument: async (id) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${API_BASE_URL}/documents/${id}`, {
        method: 'DELETE'
      })
      if (!response.ok) throw new Error('Failed to delete document')
      
      // Remove from local state
      const documents = get().documents.filter(doc => doc.id !== id)
      set({ documents, isLoading: false })
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
    }
  },
  
  startGeneration: async (documentId) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${API_BASE_URL}/generate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ document_id: documentId })
      })
      if (!response.ok) throw new Error('Failed to start generation')
      set({ isLoading: false })
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
    }
  },
  
  fetchGenerationStatus: async (documentId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/generation-status/${documentId}`)
      if (!response.ok) throw new Error('Failed to fetch generation status')
      const status = await response.json()
      set({ generationStatus: status })
    } catch (error) {
      set({ error: (error as Error).message })
    }
  },
  
  fetchDocumentChapters: async (documentId) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${API_BASE_URL}/documents/${documentId}/chapters`)
      if (!response.ok) throw new Error('Failed to fetch chapters')
      const chapters = await response.json()
      set({ currentChapters: chapters, isLoading: false })
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
    }
  },
  
  checkGrammar: async (text) => {
    try {
      const response = await fetch(`${API_BASE_URL}/check-grammar`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text })
      })
      if (!response.ok) throw new Error('Failed to check grammar')
      const result = await response.json()
      return result.corrected_text
    } catch (error) {
      throw new Error((error as Error).message)
    }
  },
  
  getWordCountEstimate: async (pages, docType, spacing) => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/word-count-estimate?pages=${pages}&doc_type=${docType}&spacing=${spacing}`
      )
      if (!response.ok) throw new Error('Failed to get word count estimate')
      const result = await response.json()
      return result.estimated_words
    } catch (error) {
      throw new Error((error as Error).message)
    }
  }
}))
