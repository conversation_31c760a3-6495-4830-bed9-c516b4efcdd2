# ASCAES Setup Guide

## 🚀 Complete Installation & Running Instructions

### Prerequisites

1. **Node.js 16+** - Download from [nodejs.org](https://nodejs.org/)
2. **Python 3.8+** - Download from [python.org](https://python.org/)
3. **Ollama** - Download from [ollama.ai](https://ollama.ai/)

### Step 1: Install Ollama and Models

```bash
# Install Ollama (if not already installed)
# Download from https://ollama.ai/

# Start Ollama service
ollama serve

# In another terminal, install required models:
ollama pull deepseek-r1:latest
ollama pull nomic-embed-text

# Verify models are installed
ollama list
```

### Step 2: Install Dependencies

**Option A: Use the automated installer**
```cmd
# Windows
install.bat

# Linux/Mac
./install.sh
```

**Option B: Manual installation**
```cmd
# Install root dependencies
npm install

# Install frontend dependencies
cd frontend-react
npm install
cd ..

# Install Python backend dependencies
cd backend-python
pip install -r requirements.txt
cd ..
```

### Step 3: Run the Application

**🎯 EASIEST WAY - Use the launcher:**
```cmd
# Double-click or run:
run-ascaes.bat
```

**Alternative - Manual start:**
```cmd
# Start all services at once
npm run dev

# OR start individually in separate terminals:
# Terminal 1 - Backend
npm run dev:backend

# Terminal 2 - Frontend  
npm run dev:frontend

# Terminal 3 - Electron
npm run dev:electron
```

## 🎯 How to Use ASCAES

### 1. Chat Interface (Main Feature)
- Go to **Chat** tab
- Type your topic or document requirements
- Click **Send** to start AI generation
- Monitor real-time progress
- Use **Settings** button for custom configurations

### 2. Document Management
- Go to **Documents** tab
- View all generated documents
- Download completed documents as PDF
- Delete unwanted documents

### 3. Settings
- Configure Ollama models
- Check connection status
- Install recommended models

## 🔧 Troubleshooting

### Backend Issues

**Error: "404 Not Found for url 'http://localhost:11434/api/generate'"**
- **Solution**: Ollama is not running or wrong endpoint
- **Fix**: 
  1. Start Ollama: `ollama serve`
  2. Verify models: `ollama list`
  3. Test connection: `curl http://localhost:11434/api/version`

**Error: "Failed to install Python dependencies"**
- **Solution**: Python or pip issues
- **Fix**:
  1. Verify Python: `python --version`
  2. Upgrade pip: `python -m pip install --upgrade pip`
  3. Install manually: `cd backend-python && pip install -r requirements.txt`

### Frontend Issues

**Error: "'concurrently' is not recognized"**
- **Solution**: Dependencies not installed
- **Fix**: `npm install` in root directory

**Error: "'wait-on' is not recognized"**
- **Solution**: Dependencies not installed  
- **Fix**: `npm install` in root directory

**Error: "Cannot connect to backend"**
- **Solution**: Backend not running
- **Fix**: Start backend: `npm run dev:backend`

### Ollama Issues

**Error: "Ollama service is not available"**
- **Solution**: Ollama not running
- **Fix**: 
  1. Start Ollama: `ollama serve`
  2. Check if running: `curl http://localhost:11434/api/version`

**Error: "Model not found"**
- **Solution**: Required models not installed
- **Fix**:
  ```bash
  ollama pull deepseek-r1:latest
  ollama pull nomic-embed-text
  ```

## 📁 Project Structure

```
E:\ASCAES\
├── run-ascaes.bat          ← 🎯 MAIN LAUNCHER
├── package.json
├── backend-python\
│   ├── app\
│   │   ├── main.py
│   │   ├── api\
│   │   ├── core\
│   │   └── db\
│   └── requirements.txt
├── frontend-react\
│   ├── src\
│   │   ├── pages\
│   │   │   ├── ChatPage.tsx     ← Main chat interface
│   │   │   ├── DocumentsPage.tsx ← PDF downloads
│   │   │   └── ...
│   │   └── components\
│   └── package.json
└── electron\
    ├── main.js
    └── preload.js
```

## 🌟 Key Features

### ✅ What's Working
- ✅ Chat interface for document generation
- ✅ Real-time progress tracking
- ✅ RAG system for document coherence
- ✅ PDF export functionality
- ✅ Document management
- ✅ Settings configuration
- ✅ Ollama integration
- ✅ Dark/Light theme
- ✅ Electron desktop app

### 🎯 How to Generate Documents

1. **Start the app**: Run `run-ascaes.bat`
2. **Go to Chat**: Click "Chat" in sidebar
3. **Enter topic**: Type your document topic/requirements
4. **Send**: Click Send button
5. **Wait**: Monitor real-time generation progress
6. **View**: Go to Documents tab to see completed document
7. **Download**: Click download button for PDF

### 🔧 Custom Settings

1. **In Chat**: Click "Settings" button
2. **Configure**: Set document type, pages, writing style, etc.
3. **Save**: Click "Create Document"
4. **Generate**: Document will be created with your settings

## 📞 Support

If you encounter issues:

1. **Check Ollama**: Make sure `ollama serve` is running
2. **Check Models**: Run `ollama list` to verify models
3. **Check Logs**: Look at terminal output for error messages
4. **Restart**: Close all terminals and run `run-ascaes.bat` again

## 🎉 Success Indicators

When everything is working:
- ✅ Backend: `http://127.0.0.1:8000` accessible
- ✅ Frontend: `http://127.0.0.1:5173` loads
- ✅ Ollama: `http://localhost:11434` responds
- ✅ Electron: Desktop app opens
- ✅ Chat: Can send messages and get responses
- ✅ Generation: Progress bars show during document creation
