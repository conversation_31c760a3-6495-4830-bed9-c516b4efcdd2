# ASCAES Technical Documentation

## Overview
ASCAES (Academic Specialist Content and Essay System) is an offline-first desktop application that generates academic documents using local AI models. The system uses Electron + React frontend with FastAPI backend and Ollama for AI generation.

## Architecture

### Frontend (React + TypeScript)
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **State Management**: Zustand store (`useAppStore`)
- **Routing**: React Router
- **Styling**: Tailwind CSS with custom dark theme
- **Icons**: Lucide React

### Backend (Python FastAPI)
- **Framework**: FastAPI with SQLAlchemy ORM
- **Database**: SQLite with async support
- **AI Integration**: Ollama API client
- **Background Tasks**: FastAPI BackgroundTasks

### AI Models
- **Embedding**: nomic-embed-text:latest
- **Generation**: deepseek-r1:1.5b
- **Service**: Ollama (local installation)

## Current Issues Fixed

### 1. Chat Message Persistence
**Problem**: Messages disappeared when navigating away from chat section
**Solution**: 
- Added localStorage persistence in `ChatPage.tsx`
- Messages are saved/loaded automatically
- Timestamps are properly serialized/deserialized

```typescript
// Load messages from localStorage on component mount
const [messages, setMessages] = useState<Message[]>(() => {
  const savedMessages = localStorage.getItem('ascaes-chat-messages')
  // ... parsing logic
})

// Save messages to localStorage when adding new messages
const addMessage = (type, content) => {
  setMessages(prev => {
    const updated = [...prev, newMessage]
    localStorage.setItem('ascaes-chat-messages', JSON.stringify(updated))
    return updated
  })
}
```

### 2. Generation Status Updates
**Problem**: Generation progress wasn't visible in chat
**Solution**:
- Enhanced progress tracking in chat messages
- Added real-time status updates during generation
- Improved polling mechanism

```typescript
// Enhanced status updates
useEffect(() => {
  if (generationStatus) {
    if (generationStatus.status === 'completed') {
      addMessage('system', 'Document generation completed!')
    } else if (generationStatus.status === 'generating') {
      const completedChapters = generationStatus.chapters.filter(ch => ch.status === 'completed').length
      const totalChapters = generationStatus.total_chapters
      if (completedChapters > 0) {
        addMessage('system', `Progress: ${completedChapters}/${totalChapters} chapters completed (${Math.round(generationStatus.progress)}%)`)
      }
    }
  }
}, [generationStatus])
```

### 3. Document Status Synchronization
**Problem**: Document status wasn't updating properly across components
**Solution**:
- Enhanced `useAppStore` to update document status when generation starts
- Synchronized status between generation API and document list
- Added progress tracking to document objects

```typescript
// Update document status when starting generation
startGeneration: async (documentId) => {
  // ... API call
  const updatedDocuments = documents.map(doc => 
    doc.id === documentId ? { ...doc, status: 'generating' } : doc
  )
  set({ documents: updatedDocuments, currentDocument: updatedCurrentDoc })
}
```

## Data Flow

### Document Generation Process
1. **User Input**: User enters topic in chat
2. **Document Creation**: `createDocument()` API call creates document record
3. **Generation Start**: `startGeneration()` triggers background task
4. **Status Polling**: Frontend polls `/generation-status/{id}` every 3 seconds
5. **Progress Updates**: Chat shows real-time progress messages
6. **Completion**: Document status changes to 'completed'

### Background Generation Flow
```python
# Backend generation flow
@router.post("/generate")
async def start_generation(request, background_tasks, db):
    # Verify document exists
    document = db.query(Document).filter(Document.id == request.document_id).first()
    
    # Start background task
    background_tasks.add_task(run_generation_wrapper, request.document_id, None)
    
    return {"message": "Generation started", "status": "generating"}

async def run_generation_wrapper(document_id, db_session):
    # Create new database session for background task
    background_db = SessionLocal()
    
    # Run generation flow
    await generation_service.run_generation_flow(document_id, background_db)
```

### State Management
The application uses Zustand for state management with the following key states:

```typescript
interface AppState {
  // Document data
  documents: Document[]
  currentDocument: Document | null
  currentChapters: Chapter[]
  
  // Generation status
  generationStatus: GenerationStatus | null
  
  // UI state
  isLoading: boolean
  error: string | null
}
```

## API Endpoints

### Documents
- `GET /api/v1/documents` - List all documents
- `POST /api/v1/documents` - Create new document
- `GET /api/v1/documents/{id}/chapters` - Get document chapters
- `DELETE /api/v1/documents/{id}` - Delete document

### Generation
- `POST /api/v1/generate` - Start document generation
- `GET /api/v1/generation-status/{id}` - Get generation status
- `POST /api/v1/grammar-check` - Check grammar

### Models
- `GET /api/v1/models` - List available Ollama models
- `GET /api/v1/models/status` - Check Ollama connection

## Database Schema

### Documents Table
```sql
CREATE TABLE documents (
    id INTEGER PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    document_type VARCHAR(100),
    status VARCHAR(50) DEFAULT 'pending',
    progress FLOAT DEFAULT 0.0,
    pages INTEGER,
    spacing VARCHAR(50),
    writing_style VARCHAR(100),
    cultural_inflection JSON,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### Chapters Table
```sql
CREATE TABLE chapters (
    id INTEGER PRIMARY KEY,
    document_id INTEGER REFERENCES documents(id),
    chapter_number INTEGER,
    title VARCHAR(500),
    content TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    word_count INTEGER DEFAULT 0,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## Configuration

### Environment Variables
- `OLLAMA_BASE_URL`: Ollama API endpoint (default: http://127.0.0.1:11434)
- `DATABASE_URL`: SQLite database path
- `API_PORT`: Backend API port (default: 8001)
- `FRONTEND_PORT`: Frontend dev server port (default: 5173)

### Ollama Models Required
- `nomic-embed-text:latest` - For text embeddings
- `deepseek-r1:1.5b` - For content generation

## Startup Process

### Automated Startup (`start-ascaes.bat`)
1. Check Ollama service status
2. Verify required models are installed
3. Check Python/Node.js dependencies
4. Start backend server (port 8001)
5. Start frontend dev server (port 5173)
6. Open browser to application
7. Monitor services with minimal logging

### Manual Startup
```bash
# Terminal 1 - Backend
cd backend-python
python -m uvicorn app.main:app --host 127.0.0.1 --port 8001 --reload

# Terminal 2 - Frontend  
cd frontend-react
npm run dev
```

## Troubleshooting

### Common Issues
1. **Ollama not running**: Check if Ollama service is started
2. **Models not found**: Ensure required models are pulled
3. **Port conflicts**: Check if ports 8001/5173 are available
4. **Database errors**: Delete `documents.db` to reset database
5. **Generation timeout**: Increase timeout in generation service

### Debug Logging
- Backend logs: `generation_debug.log`
- Frontend console: Browser developer tools
- Ollama logs: Check Ollama service logs

## Performance Considerations

### Generation Speed
- DeepSeek model: ~30-60 seconds per chapter
- Full document (10 chapters): 5-10 minutes
- Timeout set to 180 seconds per request

### Memory Usage
- Ollama models: ~2-4GB RAM
- Backend: ~100-200MB
- Frontend: ~50-100MB

### Optimization Tips
- Keep Ollama service running
- Use persistent connections
- Implement connection pooling
- Cache model responses where appropriate
